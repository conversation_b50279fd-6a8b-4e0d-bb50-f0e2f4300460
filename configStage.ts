import { getCurrentLng } from './src/trans/i18n.ts';

export function getConfig(env: ImportMetaEnv) {
  const config = {
    dev: {
      GOOGLE_CALENDAR_SERVICE: env.VITE_dev_GOOGLE_CALENDAR_SERVICE,
      GRAPHQL_URL: env.VITE_dev_GRAPHQL_URL,
      GRAPHQL_KEY: env.VITE_dev_GRAPHQL_KEY,
      DOMAIN: env.VITE_dev_DOMAIN,
      GA_PROPERTY_ID: env.VITE_dev_GA_PROPERTY_ID,
    },
    prod: { // Leave dev variables for now
      GOOGLE_CALENDAR_SERVICE: env.VITE_prod_GOOGLE_CALENDAR_SERVICE,
      GRAPHQL_URL: env.VITE_dev_GRAPHQL_URL,
      GRAPHQL_KEY: env.VITE_dev_GRAPHQL_KEY,
      DOMAIN: env.VITE_dev_DOMAIN,
      GA_PROPERTY_ID: env.VITE_dev_GA_PROPERTY_ID,
    },
  };
  const possibleStages = Object.keys(config) as unknown as (keyof typeof config);

  const _stage: string = env.VITE_STAGE;
  if (!_stage) throw Error('Set VITE_STAGE environmental variable');
  if (!possibleStages.includes(_stage))
    throw Error(`Stage ${_stage} doesn't exist`);

  const stage = _stage as typeof possibleStages;

  return {
    ...config[stage],
    STAGE: stage,
    SUPPORT_EMAIL: () => {
      switch (getCurrentLng()) {
        case 'es': return '<EMAIL>';
        default: return '<EMAIL>';
      }
    },
  }
}