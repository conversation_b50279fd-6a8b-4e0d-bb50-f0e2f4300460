import { fallbackLng } from './trans/i18n.ts';
import { checkErrorsExist } from './modules/shared/storeUtils.ts';
import { toast } from 'sonner';
import { BaseErrorDto, isUnexpectedError } from '@s4nt14go/book-backend';
import { PossibleTimeUnits } from './components/SelectTimeUnits.tsx';
import { UAParser } from 'ua-parser-js';
import { AppStateData } from './modules/shared/AppState.ts';

export const ua = new UAParser().getResult();

export const disabledClasses = 'opacity-50 cursor-not-allowed outline-none'; // "outline-none" debe ir sin "data-[disabled]:" adelante para que no se muestre el borde azul con :focus-visible
export const container = 'p-6 max-w-6xl mx-auto text-gray-700 py-2'; // For common pages with forms
export const containerMaxW = 'p-6 max-mx-auto text-gray-700 py-2'; // For pages showing wide tables as with customers and reservations
export const smFull = 'max-sm:!w-full';
export const w500smFull = `w-[500px] ${smFull}`;
export const w350smFull = `w-[350px] ${smFull}`;

export const bundle = 'mb-3';
export const title = 'mb-1';

export function isNull(some: unknown) {
  return some === null || some === undefined;
}

type Diff<T, U> = T extends U ? never : T; // Remove types from T that are assignable to U
// Type of { ...L, ...R }
export type Spread<L, R> =
  // Properties in L that don't exist in R
  Pick<L, Diff<keyof L, keyof R>> & R;

export function clone<Data>(obj: Data) {
  return JSON.parse(JSON.stringify(obj)) as Data;
}

function getLocale() {
  return navigator.languages?.length
    ? navigator.languages[0]
    : // @ts-expect-error - not typed
      navigator.userLanguage ||
        navigator.language ||
        // @ts-expect-error - not typed
        navigator.browserLanguage ||
        fallbackLng;
}
export function formatNumber(n: number) {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
  return new Intl.NumberFormat(getLocale()).format(n);
}

export const getOffset = (timeZone: string) => {
  return Intl.DateTimeFormat(undefined, {
    timeZoneName: 'short',
    timeZone,
  })
    .formatToParts()
    .find((i: { type: string; value: string }) => {
      return i.type === 'timeZoneName';
    })!.value;
};

export function trim(s: string) {
  const trimmed = s.trim();
  return trimmed.replace(/\s+/g, ' '); // replace duplicate spaces by just one
}

export type Inputted<T> = {
  entered: string;
  value: null | T;
};
export const iniInput = { entered: '', value: null };
export const iniInput2 = {
  entered: '',
  value: null as null | string,
  errorsBK: null as null | string[],
  errorsRT: null as null | string[],
};
export type Input2 = typeof iniInput2;
export const iniInput2Number = {
  entered: '',
  value: null as null | number,
  errorsBK: null as null | string[],
  errorsRT: null as null | string[],
};
export type Input2Number = typeof iniInput2Number;

export const getFieldErrors = (args: {
  errors: [BaseErrorDto, ...BaseErrorDto[]];
  field: string;
}): {
  msgs: null,
  errors: null,
} | {
  msgs: [string, ...string[]],
  errors: [BaseErrorDto, ...BaseErrorDto[]],
} => {
  const { errors, field } = args;
  const fieldErrors = errors.filter((e) => e.field?.startsWith(field));
  const fieldErrorsMsg: null | string[] = fieldErrors.map((e) => e.message);
  if (!fieldErrorsMsg.length) 
  return {
    msgs: null,
    errors: null,
  } 
  return {
    msgs: fieldErrorsMsg as [string, ...string[]],
    errors: fieldErrors as [BaseErrorDto, ...BaseErrorDto[]],
  }
}

export function getInvalidIntError(
  value: null | number,
  nullMsg?: string,
): [string, ...string[]] | null {
  if (value === null)
    return nullMsg === undefined ? ['Ingrese un valor'] : [nullMsg];
  else if (Math.abs(value) > 2147483647)
    // Max GraphQL Int
    return ['El valor ingresado excede el máximo permitido'];
  else return null;
}

export function showErrorsWithToast(args: {
  errors: BaseErrorDto[] | null;
  msg: string;
}) {
  console.log('showErrorsWithToast', args);
  const { errors, msg } = args;
  const _errors = checkErrorsExist(errors);
  const messages = _errors.map((e) => e.message);
  toast.error(
    <div>
      <div className="font-bold">
        {`Error${_errors.length > 1 ? 'es' : ''} al intentar ${msg}`}:
      </div>
      {messages.map((m, i) => (
        <div key={i}>• {m}</div>
      ))}
    </div>,
  );
}

export function showUnexpectedErrorDetails(args: {
  errors: BaseErrorDto[] | null;
  msg?: string;
}) {
  console.log('showUnexpectedErrorDetails', args);
  const { errors, msg } = args;
  const _errors = checkErrorsExist(errors);

  const unexpectedErrors = _errors.filter(isUnexpectedError);
  if (unexpectedErrors.length > 1) {
    console.log({ unexpectedErrors, _errors });
    throw Error('More than one UnexpectedError');
  }
  if (unexpectedErrors.length === 1) {
    const unexpectedError = unexpectedErrors[0];
    console.log({ unexpectedError });
    toast.error(
      <div>
        <div className="font-bold">
          {`Error no esperado${msg ? ` al intentar ${msg}` : ''}`}:
        </div>
        <div>• Función: {unexpectedError.logGroup}</div>
        <div>• ID: {unexpectedError.requestId}</div>
      </div>,
    );
  } else {
    return _errors.filter((e) => !isUnexpectedError(e)) as [
      BaseErrorDto,
      ...BaseErrorDto[],
    ];
  }
}

export function getStringAndUnitFromValue(value: number | null) {
  const compound: {
    entered: string;
    unit: PossibleTimeUnits;
  } = {
    entered: '',
    unit: 'min',
  };

  label1: if (value !== null) {
    compound.entered = value.toString();
    if (value === 0) break label1;
    const hours = value / 60;
    if (Number.isInteger(hours)) {
      compound.unit = 'hr';
      compound.entered = hours.toString();
    }
    const days = hours / 24;
    if (Number.isInteger(days)) {
      compound.unit = 'day';
      compound.entered = days.toString();
    }
  }
  return compound;
}
export function getValueFromStringAndUnit(args: {
  entered: string;
  unit: PossibleTimeUnits;
}) {
  const { entered, unit } = args;
  const value = Number(entered);
  if (unit === 'min') return value;
  if (unit === 'hr') return value * 60;
  if (unit === 'day') return value * 60 * 24;
  throw Error(`Invalid unit ${unit}`);
}

export const getH24 = (userTz: string) => ({
  timeZone: userTz,
  hour: '2-digit' as const,
  minute: '2-digit' as const,
  hourCycle: 'h23' as const,
});
export function getDateFormat(userTz?: string) {
  return {
    ...userTz? { timeZone: userTz} : {},
    year: 'numeric' as const,
    month: '2-digit' as const,
    day: '2-digit' as const,
  };
}

export const getInputtedValue = (entered: string) => {
  const trimmed = trim(entered);
  return trimmed === '' ? null : trimmed;
};
export const getInputtedValueNumber = (entered: string) => {
  const trimmed = trim(entered);
  return trimmed === '' ? null : Number(trimmed);
};

// This function is used by MCustomer table created_at and updated_at fields, so not setting the user's timezone it's ok
export function format2BrowserDate(args: { value: string }) {
  const { value } = args;
  return new Date(value).toLocaleDateString(undefined, getDateFormat());
}

// This function is used by Reservation table, so setting the user's timezone is required
export function format2DateTime(args: { value: string; tz: string }) {
  const { value, tz } = args;
  return new Date(value)
    .toLocaleDateString(undefined, {
      ...getDateFormat(tz),
      ...getH24(tz),
    })
    .replace(/,/g, '');
}

export const getCurrentBalance = (appState: AppStateData) => appState.balance.rows?.slice(0).sort(r => r.index).reverse()[0].balance ?? null;
