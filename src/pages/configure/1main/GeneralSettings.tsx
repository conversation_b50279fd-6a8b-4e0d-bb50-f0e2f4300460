import { Input } from '@headlessui/react';
import RichTextEditor from '../../../components/RichTextEditor';
import {
  bundle,
  clone,
  disabledClasses,
  getFieldErrors,
  getInputtedValueNumber,
  getStringAndUnitFromValue,
  getValueFromStringAndUnit,
  showErrorsWithToast,
} from '../../../utils.tsx';
import { processResponse } from '../../../modules/shared/storeUtils.ts';
import { useImmer } from 'use-immer';
import { useEffect } from 'react';
import { toast } from 'sonner';
import {
  PossibleTimeUnits,
  SelectTimeUnits,
} from '../../../components/SelectTimeUnits.tsx';
import { reservation, getBackendLng, BaseErrorDto } from '@s4nt14go/book-backend';
import { getCurrentLng } from '../../../trans/i18n.ts';
import { AppState } from '../../../modules/shared/AppState.ts';
import { MyButton } from '../../../components/MyButton.tsx';
import {
  launchSaveGSreservations,
  SaveGSreservationsArgs,
} from '../../../modules/reservation/saveGSreservations.ts';
import { Errors } from '../../../components/Errors.tsx';
import {
  useReservationStore,
} from '../../../modules/reservation/store/reservationStore.ts';
import { MyCheckbox } from '../../../components/MyCheckbox.tsx';
import { SeparatorLeft } from '../../../components/SeparatorLeft.tsx';

type GetGSforReservationsResponse = reservation.GetGSforReservationsResponse;
const validateArgs = reservation.validateUpdateGSreservationsRequest;

function GeneralSettings() {
  const [local, updateLocal] = useImmer(() => {
    const appState = AppState.getState();
    const { maxDaysAhead, minTimeBeforeService, phoneRequired, cancellationUpTo, maxSimultaneousReservations, reminderTemplate } = appState.reservation.gs;
    return {
      openedAt: null as null | number,

      maxDaysAhead: {
        value: maxDaysAhead,
        entered: maxDaysAhead ?? '',
        errorsBK: null as null | string[], // Errors coming from backend responses
        errorsRT: null as null | string[], // Errors coming from real-time validations
      },
      maxSimultaneousReservations: {
        value: maxSimultaneousReservations,
        entered: maxSimultaneousReservations ?? '',
        errorsBK: null as null | string[], // Errors coming from backend responses
        errorsRT: null as null | string[], // Errors coming from real-time validations
      },
      minTimeBeforeService: {
        value: minTimeBeforeService,
        ...getStringAndUnitFromValue(minTimeBeforeService), // get entered and unit
        errorsBK: null as null | string[], // Errors coming from backend responses
        errorsRT: null as null | string[], // Errors coming from real-time validations
      },
      phoneRequired: phoneRequired ?? true, // if phoneRequired wasn't still set in appState (is null), default to true
      cancellationUpTo: {
        value: cancellationUpTo,
        ...getStringAndUnitFromValue(cancellationUpTo), // get entered and unit
        errorsBK: null as null | string[], // Errors coming from backend responses
        errorsRT: null as null | string[], // Errors coming from real-time validations
      },
      whatsAppTemplate: {
        value: convertWhatsAppFormat2html(reminderTemplate),
        entered: convertWhatsAppFormat2html(reminderTemplate) ?? '',
        errorsBK: null as null | string[], // Errors coming from backend responses
        errorsRT: null as null | string[], // Errors coming from real-time validations
      },

      isSaving: false,
      falseOrArgs: false as false | SaveGSreservationsArgs,
    };
  });
  const { openedAt, falseOrArgs, minTimeBeforeService, isSaving, maxDaysAhead, phoneRequired, cancellationUpTo, maxSimultaneousReservations, whatsAppTemplate } =
    local;

  useEffect(() => {
    updateLocal((draft) => {
      draft.openedAt = Date.now();
    });
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const reservationStore = useReservationStore();
  function selectTimeUnitsChangedForMinTimeBeforeService(unit: PossibleTimeUnits) {
    updateLocal((draft) => {
      draft.minTimeBeforeService.unit = unit;
    });
  }
  function selectTimeUnitsChangedForCancellationUpTo(unit: PossibleTimeUnits) {
    updateLocal((draft) => {
      draft.cancellationUpTo.unit = unit;
    });
  }

  // region getGSforReservations
  // Launched in parent Configure.tsx
  useEffect(() => {
    processResponse({
      requestName: 'getGSforReservations',
      request: reservationStore.getGSforReservations,
      openedAt,
      errorMsg: 'obtener las opciones generales.',
      showData() {
        const appState = AppState.getState();
        let { phoneRequired, maxSimultaneousReservations, maxDaysAhead, minTimeBeforeService, cancellationUpTo, reminderTemplate } = appState.reservation.gs;
        // Since applyDataReceived() from getGSforReservations.ts should have been already called at this point, we can cast them to the GetGSforReservationsResponse types:
        // eslint-disable-next-line @typescript-eslint/non-nullable-type-assertion-style
        phoneRequired = phoneRequired as GetGSforReservationsResponse['phoneRequired'];
        // eslint-disable-next-line @typescript-eslint/non-nullable-type-assertion-style
        maxSimultaneousReservations = maxSimultaneousReservations as GetGSforReservationsResponse['maxSimultaneousReservations'];
        // eslint-disable-next-line @typescript-eslint/non-nullable-type-assertion-style
        maxDaysAhead = maxDaysAhead as GetGSforReservationsResponse['reservationLimits']['maxDaysAhead'];
        // eslint-disable-next-line @typescript-eslint/non-nullable-type-assertion-style
        minTimeBeforeService = minTimeBeforeService as GetGSforReservationsResponse['reservationLimits']['minTimeBeforeService'];
        // eslint-disable-next-line @typescript-eslint/non-nullable-type-assertion-style
        cancellationUpTo = cancellationUpTo as GetGSforReservationsResponse['cancellationUpTo'];
        // eslint-disable-next-line @typescript-eslint/non-nullable-type-assertion-style
        reminderTemplate = reminderTemplate as GetGSforReservationsResponse['reminderTemplate'];

        const {
          entered: minTimeBeforeServiceEntered,
          unit: minTimeBeforeServiceUnit,
        } = getStringAndUnitFromValue(minTimeBeforeService);
        const {
          entered: cancellationUpToEntered,
          unit: cancellationUpToUnit,
        } = getStringAndUnitFromValue(cancellationUpTo);
        updateLocal((draft) => {
          draft.phoneRequired = phoneRequired;

          draft.maxSimultaneousReservations.value = maxSimultaneousReservations;
          draft.maxSimultaneousReservations.entered = maxSimultaneousReservations.toString();
          draft.maxSimultaneousReservations.errorsBK = null;
          draft.maxSimultaneousReservations.errorsRT = null;

          draft.maxDaysAhead.value = maxDaysAhead;
          draft.maxDaysAhead.entered = maxDaysAhead.toString();
          draft.maxDaysAhead.errorsBK = null;
          draft.maxDaysAhead.errorsRT = null;

          draft.minTimeBeforeService.value = minTimeBeforeService;
          draft.minTimeBeforeService.entered = minTimeBeforeServiceEntered;
          draft.minTimeBeforeService.unit = minTimeBeforeServiceUnit;
          draft.minTimeBeforeService.errorsBK = null;
          draft.minTimeBeforeService.errorsRT = null;

          draft.cancellationUpTo.value = cancellationUpTo;
          draft.cancellationUpTo.entered = cancellationUpToEntered;
          draft.cancellationUpTo.unit = cancellationUpToUnit;
          draft.cancellationUpTo.errorsBK = null;
          draft.cancellationUpTo.errorsRT = null;

          draft.whatsAppTemplate.value = convertWhatsAppFormat2html(reminderTemplate);
          draft.whatsAppTemplate.entered = convertWhatsAppFormat2html(reminderTemplate);
          draft.whatsAppTemplate.errorsBK = null;
          draft.whatsAppTemplate.errorsRT = null;
        });
      },
    });
  }, [reservationStore.getGSforReservations.time]); // eslint-disable-line react-hooks/exhaustive-deps
  // endregion

  // region saveGSreservations
  useEffect(() => {
    const { timeIsOk, inProgress } = processResponse({
      requestName: 'saveGSreservations',
      request: reservationStore.saveGSreservations,
      openedAt,
      showData() {
        toast.success('Opciones generales guardadas');
        updateLocal((draft) => {
          draft.maxSimultaneousReservations.errorsBK = null;
          draft.maxDaysAhead.errorsBK = null;
          draft.minTimeBeforeService.errorsBK = null;
          draft.cancellationUpTo.errorsBK = null;
          draft.whatsAppTemplate.errorsBK = null;
        });
      },
      showErrors(errorsBK) {

        let unhandled: BaseErrorDto[] = clone(errorsBK);

        // Handle errors by type showing an individual toast for each
        for (const [i, error] of errorsBK.entries()) {
          switch (error.type) {
            case 'UpdateGSreservationsErrors.NotFound':
              toast.error(error.message);
              unhandled.splice(i, 1);
              break;
          }
        }

        // Handle errors by field reflected in the form.
        // These are the errors that have a setField() in the backend use case controller:
        // maxSimultaneousReservations, maxDaysAhead, cancellationUpTo, minTimeBeforeService
        updateLocal((draft) => {

          const fieldErrors: BaseErrorDto[] = [];

          let got = getFieldErrors({
            errors: errorsBK,
            field: 'maxSimultaneousReservations',
          })
          draft.maxSimultaneousReservations.errorsBK = got.msgs;
          unhandled = unhandled.filter((e) => e.field !== 'maxSimultaneousReservations');
          if (got.errors) fieldErrors.push(...got.errors);

          got = getFieldErrors({
            errors: errorsBK,
            field: 'maxDaysAhead',
          })
          draft.maxDaysAhead.errorsBK = got.msgs;
          unhandled = unhandled.filter((e) => e.field !== 'maxDaysAhead');
          if (got.errors) fieldErrors.push(...got.errors);

          got = getFieldErrors({
            errors: errorsBK,
            field: 'minTimeBeforeService',
          })
          draft.minTimeBeforeService.errorsBK = got.msgs;
          unhandled = unhandled.filter((e) => e.field !== 'minTimeBeforeService');
          if (got.errors) fieldErrors.push(...got.errors);

          got = getFieldErrors({
            errors: errorsBK,
            field: 'cancellationUpTo',
          })
          draft.cancellationUpTo.errorsBK = got.msgs;
          unhandled = unhandled.filter((e) => e.field !== 'cancellationUpTo');
          if (got.errors) fieldErrors.push(...got.errors);

          got = getFieldErrors({
            errors: errorsBK,
            field: 'reminderTemplate',
          })
          draft.whatsAppTemplate.errorsBK = got.msgs;
          unhandled = unhandled.filter((e) => e.field !== 'reminderTemplate');
          if (got.errors) fieldErrors.push(...got.errors);

          // Show messages related to the fields in a single toast
          if (fieldErrors.length) showErrorsWithToast({
            errors: fieldErrors,
            msg: 'guardar opciones generales. Revise el formulario',
          });

          // Show unhandled errors in a single toast
          if (unhandled.length) showErrorsWithToast({
            errors: unhandled,
            msg: 'guardar opciones generales',
          });

        });


      },
    });
    if (!timeIsOk) return;
    updateLocal((draft) => {
      draft.isSaving = inProgress;
    });
  }, [reservationStore.saveGSreservations.time]); // eslint-disable-line react-hooks/exhaustive-deps
  // endregion

  // Clear backend errors when inputs are edited
  useEffect(() => {
    updateLocal((draft) => {
      draft.maxSimultaneousReservations.errorsBK = null;
    });
  }, [ maxSimultaneousReservations.value ]); // eslint-disable-line react-hooks/exhaustive-deps
  useEffect(() => {
    updateLocal((draft) => {
      draft.cancellationUpTo.errorsBK = null;
    });
  }, [ cancellationUpTo.value, cancellationUpTo.unit ]); // eslint-disable-line react-hooks/exhaustive-deps

  // Since saveGSreservations request may give an error that depends on the relation between the two values, we clear them together:
  useEffect(() => {
    updateLocal((draft) => {
      draft.maxDaysAhead.errorsBK = null;
      draft.minTimeBeforeService.errorsBK = null;
    });
  }, [maxDaysAhead.value, minTimeBeforeService.value, minTimeBeforeService.unit]); // eslint-disable-line react-hooks/exhaustive-deps

  // Clear backend errors when whatsAppTemplate is edited
  useEffect(() => {
    updateLocal((draft) => {
      draft.whatsAppTemplate.errorsBK = null;
    });
  }, [whatsAppTemplate.value]); // eslint-disable-line react-hooks/exhaustive-deps

  // Real-time checks and write falseOrArgs with a valid SaveGSreservationsArgs if checks pass
  useEffect(() => {
    // First check for that conditions that aren't handled by validateArgs(), this means when they're null
    const maxDaysAheadErrors =
      maxDaysAhead.value === null
        ? ['Ingrese la cantidad de días.']
        : null;
    const minTimeBeforeServiceErrors =
      minTimeBeforeService.value === null
        ? ['Ingrese la antelación mínima.']
        : null;
    const maxSimultaneousReservationsErrors =
      maxSimultaneousReservations.value === null
        ? ['Ingrese la cantidad máxima.']
        : null;
    const cancellationUpToErrors =
      cancellationUpTo.value === null
        ? ['Ingrese la antelación mínima.']
        : null;

    // whatsAppTemplate.value will always be a string, but since appState.reservation.gs.reminderTemplate can be null before appState is initialized, I’ve kept the following to work smoothly with TypeScript.
    const whatsAppTemplateErrors =
      whatsAppTemplate.value === null
        ? ['Esperando por inicialización de appState...']
        : null;

    if (
      maxDaysAheadErrors ||
      minTimeBeforeServiceErrors ||
      maxSimultaneousReservationsErrors ||
      cancellationUpToErrors ||
      whatsAppTemplateErrors
    ) {
      updateLocal((draft) => {
        draft.maxDaysAhead.errorsRT = maxDaysAheadErrors;
        draft.minTimeBeforeService.errorsRT = minTimeBeforeServiceErrors;
        draft.maxSimultaneousReservations.errorsRT = maxSimultaneousReservationsErrors;
        draft.cancellationUpTo.errorsRT = cancellationUpToErrors;
        draft.whatsAppTemplate.errorsRT = whatsAppTemplateErrors;
        draft.falseOrArgs = false;
      });

    } else {

      // At this point values that can't be null, aren't null
      const reminderTemplate = convertHtml2whatsAppFormat(whatsAppTemplate.value);
      const minTimeBeforeServiceValue = getValueFromStringAndUnit({
        entered: minTimeBeforeService.entered,
        unit: minTimeBeforeService.unit,
      });
      const cancellationUpToValue = getValueFromStringAndUnit({
        entered: cancellationUpTo.entered,
        unit: cancellationUpTo.unit,
      });
      const { errors } = validateArgs({
        maxDaysAhead: maxDaysAhead.value!,
        minTimeBeforeService: minTimeBeforeServiceValue,
        maxSimultaneousReservations: maxSimultaneousReservations.value!,
        cancellationUpTo: cancellationUpToValue,
        reminderTemplate,
        lng: getBackendLng(getCurrentLng()),
      });

      if (errors) {

        updateLocal((draft) => {
          draft.maxDaysAhead.errorsRT = getFieldErrors({
            errors,
            field: 'maxDaysAhead',
          }).msgs;
          draft.minTimeBeforeService.errorsRT = getFieldErrors({
            errors,
            field: 'minTimeBeforeService',
          }).msgs;
          draft.maxSimultaneousReservations.errorsRT = getFieldErrors({
            errors,
            field: 'maxSimultaneousReservations',
          }).msgs;
          draft.cancellationUpTo.errorsRT = getFieldErrors({
            errors,
            field: 'cancellationUpTo',
          }).msgs;
          draft.whatsAppTemplate.errorsRT = getFieldErrors({
            errors,
            field: 'reminderTemplate',
          }).msgs;
          draft.falseOrArgs = false;
        });
      } else {
        updateLocal((draft) => {
          draft.maxDaysAhead.errorsRT = null;
          draft.minTimeBeforeService.errorsRT = null;
          draft.maxSimultaneousReservations.errorsRT = null;
          draft.cancellationUpTo.errorsRT = null;
          draft.whatsAppTemplate.errorsRT = null;

          if (
            maxDaysAhead.errorsBK ||
            minTimeBeforeService.errorsBK ||
            maxSimultaneousReservations.errorsBK ||
            cancellationUpTo.errorsBK ||
            whatsAppTemplate.errorsBK
          )
            draft.falseOrArgs = false;
          else
            draft.falseOrArgs = {
              maxDaysAhead: maxDaysAhead.value!,
              minTimeBeforeService: minTimeBeforeServiceValue,
              maxSimultaneousReservations: maxSimultaneousReservations.value!,
              cancellationUpTo: cancellationUpToValue,
              phoneRequired: phoneRequired,
              reminderTemplate,
            };
        });
      }
    }
  }, [
    maxDaysAhead.value,
    maxDaysAhead.errorsBK,
    minTimeBeforeService.value,
    minTimeBeforeService.unit,
    minTimeBeforeService.errorsBK,
    maxSimultaneousReservations.value,
    maxSimultaneousReservations.errorsBK,
    cancellationUpTo.value,
    cancellationUpTo.unit,
    cancellationUpTo.errorsBK,
    whatsAppTemplate.value,
    whatsAppTemplate.errorsBK,
    updateLocal,
    phoneRequired, // it doesn't actually matter, but it's included to avoid a missing dependency warning
  ]);

  return (
    <>

      <SeparatorLeft title='OPCIONES GENERALES' />

      <MyCheckbox
        disabled={local.isSaving}
        checked={local.phoneRequired}
        classes='mb-2'
        onClick={(to) => {
          updateLocal((draft) => {
            draft.phoneRequired = to;
          });
        }}
        checkedText={phoneRequiredText}
        uncheckedText={phoneRequiredText}
        withError={false}
      />

      <div className={bundle}>
        <p className='inline'>Cantidad máxima de reservaciones simultáneas &nbsp;</p>
        <div className='rounded-md inline'>
          <Input
            type='number'
            disabled={isSaving}
            className={`
              border border-gray-400 focus:outline-none px-3 py-1.5 rounded-lg text-gray mr-2 max-w-20
              ${isSaving ? disabledClasses : ''}
              ${maxSimultaneousReservations.errorsBK || maxSimultaneousReservations.errorsRT ? 'border-red-400' : ''}
            `}
            value={maxSimultaneousReservations.entered}
            onChange={(e) => {
              const entered = e.target.value;
              updateLocal((draft) => {
                draft.maxSimultaneousReservations.value = getInputtedValueNumber(entered);
                draft.maxSimultaneousReservations.entered = entered;
              });
            }}
          />
        </div>

        <Errors messages={maxSimultaneousReservations.errorsBK} />
        <Errors messages={maxSimultaneousReservations.errorsRT} />
      </div>

      <p className='font-semibold mb-3'>Rango permitido para reservaciones de usuarios</p>

      <div className={bundle}>
        <p className='inline'>Permitir reservas dentro de los siguientes &nbsp;</p>
        <div className='rounded-md inline'>
          <Input
            type='number'
            disabled={isSaving}
            className={`
              border border-gray-400 focus:outline-none px-3 py-1.5 rounded-lg text-gray mr-2 max-w-20
              ${isSaving ? disabledClasses : ''}
              ${maxDaysAhead.errorsBK || maxDaysAhead.errorsRT ? 'border-red-400' : ''}
            `}
            value={maxDaysAhead.entered}
            onChange={(e) => {
              const entered = e.target.value;
              updateLocal((draft) => {
                draft.maxDaysAhead.value = getInputtedValueNumber(entered);
                draft.maxDaysAhead.entered = entered;
              });
            }}
          />
          días al momento de reservar.
        </div>

        <Errors messages={maxDaysAhead.errorsBK} />
        <Errors messages={maxDaysAhead.errorsRT} />
      </div>

      <div className={bundle}>
        <p className='inline'>
          Antelación mínima requerida para la prestación del servicio al momento de
          reservar: &nbsp;
        </p>
        <div className='rounded-md inline'>
          <Input
            type='number'
            min={0}
            disabled={isSaving}
            className={`
              border border-gray-400 focus:outline-none px-3 py-1.5 rounded-lg text-gray mr-2 max-w-20
              ${isSaving ? disabledClasses : ''}
              ${minTimeBeforeService.errorsBK || minTimeBeforeService.errorsRT ? 'border-red-400' : ''}
            `}
            value={minTimeBeforeService.entered}
            onChange={(e) => {
              const entered = e.target.value;
              updateLocal((draft) => {
                draft.minTimeBeforeService.value = getInputtedValueNumber(entered);
                draft.minTimeBeforeService.entered = entered;
              });
            }}
          />
          <SelectTimeUnits
            changed={selectTimeUnitsChangedForMinTimeBeforeService}
            inProgress={isSaving}
            value={minTimeBeforeService.unit}
          />
        </div>

        <Errors messages={minTimeBeforeService.errorsBK} />
        <Errors messages={minTimeBeforeService.errorsRT} />
      </div>

      <p className='font-semibold mb-3'>Rango permitido para cancelaciones de usuarios</p>

      <div className={bundle}>
        <p className='inline'>
          Permitir cancelaciones hasta &nbsp;
        </p>
        <div className='rounded-md inline'>
          <Input
            type='number'
            disabled={isSaving}
            className={`
              border border-gray-400 focus:outline-none mb-1.5 px-3 py-1.5 rounded-lg text-gray mr-2 max-w-20
              ${isSaving ? disabledClasses : ''}
              ${cancellationUpTo.errorsBK || local.cancellationUpTo.errorsRT ? 'border-red-400' : ''}
            `}
            value={cancellationUpTo.entered}
            onChange={(e) => {
              const entered = e.target.value;
              updateLocal((draft) => {
                draft.cancellationUpTo.value = getInputtedValueNumber(entered);
                draft.cancellationUpTo.entered = entered;
              });
            }}
          />
          <SelectTimeUnits
            changed={selectTimeUnitsChangedForCancellationUpTo}
            inProgress={isSaving}
            value={cancellationUpTo.unit}
          />
          &nbsp; antes del comienzo del servicio.
        </div>

        <Errors messages={cancellationUpTo.errorsBK} />
        <Errors messages={cancellationUpTo.errorsRT} />
      </div>

      <p className='font-semibold mb-3 mt-5'>Mensaje de WhatsApp</p>

      <div className={bundle}>
        <p className='mb-2'>Plantilla para mensajes de WhatsApp:</p>
        <div className='rounded-md'>
          <RichTextEditor
            disabled={isSaving}
            className='mb-2'
            value={whatsAppTemplate.entered}
            error={!!(whatsAppTemplate.errorsBK || whatsAppTemplate.errorsRT)}
            placeholder='Escribe el mensaje para WhatsApp aquí...'
            onChange={(html) => {
              updateLocal((draft) => {
                draft.whatsAppTemplate.value = html;
                draft.whatsAppTemplate.entered = html;
              });
            }}
          />
        </div>

        <Errors messages={whatsAppTemplate.errorsBK} />
        <Errors messages={whatsAppTemplate.errorsRT} />

        <p className='text-sm text-gray-600 mb-4'>
          Variables disponibles: <br />
          <code>{'{firstName}'}</code> - Nombre del cliente<br />
          <code>{'{service}'}</code> - Nombre del servicio<br />
          <code>{'{day}'}</code> - Día de la reserva<br />
          <code>{'{time}'}</code> - Hora de la reserva<br />
          <br />
          <span className='font-medium'>Para darle formato al mensaje de WhatsApp:</span><br />
          <span>El texto en <strong>negrita</strong> se mostrará como *negrita*</span><br />
          <span>El texto en <em>cursiva</em> se mostrará como _cursiva_</span><br />
          <span>El texto <s>tachado</s> se mostrará como ~tachado~</span><br />
        </p>
      </div>

      <MyButton
        checkOnClick={falseOrArgs}
        inProgress={isSaving}
        request={launchSaveGSreservations}
        color='brand'
        text='GUARDAR'
      />
    </>
  );
}

export default GeneralSettings;

const phoneRequiredText = 'Sólo usuarios con teléfono verificado pueden reservar.';

function convertHtml2whatsAppFormat(html: string) {
  let whatsappFormattedText = html;
  // console.log('html', html);
  if (html.includes('<')) {
    // Convert HTML formatting to WhatsApp formatting
    whatsappFormattedText = whatsappFormattedText
      // Convert bold tags to WhatsApp *bold*
      .replace(/<strong[^>]*>|<b[^>]*>/gi, '*')
      .replace(/<\/strong>|<\/b>/gi, '*')
      // Convert italic tags to WhatsApp _italic_
      .replace(/<em[^>]*>|<i[^>]*>/gi, '_')
      .replace(/<\/em>|<\/i>/gi, '_')
      // Convert strikethrough to WhatsApp ~strikethrough~
      .replace(/<del[^>]*>|<s[^>]*>/gi, '~')
      .replace(/<\/del>|<\/s>/gi, '~')
      // Convert line breaks to actual line breaks
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<p[^>]*>/gi, '')
      // if </p> is the last tag, remove it without adding a new line
      .replace(/<\/p>\s*$/gi, '')
      .replace(/<\/p>/gi, '\n')
      // Remove any other HTML tags
      .replace(/<[^>]*>/g, '')
      // Replace HTML entities
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      // Remove empty bold, italic, and strikethrough
      .replace(/(\*{2,}|_{2,}|~{2,})/g, '')
      // Allow a maximum of 3 new lines
      .replace(/\n{4,}/g, '\n\n\n');
  }
  return whatsappFormattedText;
}

/**
 * Converts WhatsApp formatted text to HTML
 *
 * This function takes text with WhatsApp formatting syntax (*bold*, _italic_, ~strikethrough~)
 * and converts it to HTML with appropriate tags (<strong>, <em>, <s>).
 *
 * @param whatsappFormattedText - The WhatsApp formatted text to convert
 * @returns The HTML formatted text
 */
function convertWhatsAppFormat2html(whatsappFormattedText: string | null): string {
  if (whatsappFormattedText === null) return '';

  // Process the text in stages to handle nested formatting correctly
  let htmlText = whatsappFormattedText;

  // First, escape any HTML special characters to prevent injection
  htmlText = htmlText
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');

  // Handle line breaks first
  htmlText = htmlText.replace(/\n/g, '<br />');

  // Process formatting with regex that handles non-nested cases
  // Bold: *text* -> <strong>text</strong>
  htmlText = htmlText.replace(/\*(.*?)\*/g, '<strong>$1</strong>');

  // Italic: _text_ -> <em>text</em>
  htmlText = htmlText.replace(/_(.*?)_/g, '<em>$1</em>');

  // Strikethrough: ~text~ -> <s>text</s>
  htmlText = htmlText.replace(/~(.*?)~/g, '<s>$1</s>');

  return htmlText;
}
