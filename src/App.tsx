import { Routes, Route, Outlet, Link, useLocation } from 'react-router-dom';
import { Fragment, useEffect } from 'react';
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Transition,
} from '@headlessui/react';
import {
  Bars3Icon,
  // BellIcon,
  XMarkIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';
import Home from './pages/Home.tsx';
import { Connection } from './pages/connection/Connection.tsx'; // poner el resto de los componentes de esta forma así salen sus nombres al usar "Components" en las DevTools de Chrome
import { Configure } from './pages/configure/Configure.tsx';
import { Profile } from './pages/profile/0Profile.tsx';
import Customers from './pages/customers/Customers.tsx';
import NoMatch from './pages/NoMatch.tsx';
import {
  AppStateNull,
  createAppState,
  getInitialStateFromLocal,
  getInitialStateFromNetwork,
  LSitem,
} from './modules/shared/AppState.ts';
import { Toaster } from 'sonner';
import { useTranslation } from 'react-i18next';
import { TransForApp } from './App.trans.ts';
import { getCurrentLng, lngs, possibleLngs } from './trans/i18n.ts';
import Schedule from './pages/schedule/Schedule.tsx';
import ReservationsNav from './pages/reservations/ReservationsNav.tsx';
import Balance from './pages/balance/Balance.tsx';
import { getConfig } from '../configStage.ts';
import ReactGA from 'react-ga4';
import Logo from './assets/logo/con-color-sin-texto.svg';

const configStage = getConfig(import.meta.env);
const { STAGE } = configStage;

const trans = new TransForApp();
const i18n = trans.getI18nForComponent();

const links = {
  demo: {
    title: 'home',
    path: '/',
    component: <Home />,
  },
  connection: {
    title: 'connection',
    path: '/connection',
    component: <Connection />,
  },
  profile: {
    title: 'profile',
    path: '/profile',
    component: <Profile />,
  },
  configure: {
    title: 'configure',
    path: '/configure',
    component: <Configure />,
  },
  schedule: {
    title: 'schedule',
    path: '/schedule',
    component: <Schedule />,
  },
  customers: {
    title: 'customers',
    path: '/customers',
    component: <Customers />,
  },
  reservations: {
    title: 'reservations',
    path: '/reservations',
    component: <ReservationsNav />,
  },
  balance: {
    title: 'balance',
    path: '/balance',
    component: <Balance />,
  },
};
const routes = {
  ...links,
  noMatch: {
    title: '',
    path: '/*',
    component: <NoMatch />,
  },
};

// Used for development tests:
// localStorage.setItem('appState', JSON.stringify({ userId: '00ad8920-c36d-11ee-8adf-3f66c88fcbdd' }));

// First create the app state with the data from localStorage
const { stateFromLocal } = getInitialStateFromLocal({
  localStorage,
});
createAppState({
  initialState: stateFromLocal,
  localStorage,
});
// Then, get data from the network
const { userId } = stateFromLocal;
if (!userId) {
  alert('Error de inicialización: userId no definido');
  if (STAGE === 'dev') {
    if (confirm('Desea setear userId a 00ad8920-c36d-11ee-8adf-3f66c88fcbdd?')) {
      localStorage.setItem(
        LSitem,
        JSON.stringify({
          ...AppStateNull(),
          userId: '00ad8920-c36d-11ee-8adf-3f66c88fcbdd',
        }),
      );
      window.location.reload();
    }
  }
} else {
  ReactGA.set({ userId });
  await getInitialStateFromNetwork(userId);
}

function App() {
  return (
    <>
      <Routes>
        <Route path="/" element={<Layout />}>
          {Object.values(routes).map((r) => {
            return <Route key={r.path} path={r.path} element={r.component} />;
          })}
        </Route>
      </Routes>
    </>
  );
}

function Layout() {
  const location = useLocation();
  const path = location.pathname;
  const { i18n: i18nRoot } = useTranslation();
  const { t } = useTranslation(trans.ns, { i18n, lng: i18nRoot.language });

  useEffect(() => {
    ReactGA.send({ hitType: "pageview", page: path + location.search });
  }, [location, path]);

  return (
    <>
      <Disclosure as="nav" className="bg-white shadow">
        {({ open }) => (
          <>
            <div className="mx-auto max-w-7xl px-2 menu:px-6 lg:px-8">
              <div className="relative flex h-16 justify-between">
                <div className="absolute inset-y-0 left-0 flex items-center menu:hidden">
                  {/* Mobile menu button */}
                  <DisclosureButton className="relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-brand-500">
                    <span className="absolute -inset-0.5" />
                    <span className="sr-only">Open main menu</span>
                    {open ? (
                      <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                    ) : (
                      <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                    )}
                  </DisclosureButton>
                </div>
                <div className="flex flex-1 items-center justify-center mr-5 menu:items-stretch menu:justify-start menu:mr-0">
                  <div className="flex flex-shrink-0 items-center">
                    <img className="h-8 w-auto" src={Logo} alt="Bookea.link" />
                  </div>
                  <div className="hidden menu:flex justify-around w-full">
                    {Object.values(links).map((r) => {
                      return (
                        <Link
                          key={r.path}
                          to={r.path}
                          className={
                            path === r.path
                              ? 'inline-flex items-center border-b-2 border-brand-500 px-1 pt-1 text-sm font-medium text-gray-900'
                              : 'inline-flex items-center border-b-2 border-transparent px-1 pt-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 transition-colors'
                          }
                        >
                          {t(r.title)}
                        </Link>
                      );
                    })}
                  </div>
                </div>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 menu:static menu:inset-auto menu:ml-6 menu:pr-0">
                  {/*<button
                    type="button"
                    className="relative rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 transition-colors"
                  >
                    <span className="absolute -inset-1.5" />
                    <span className="sr-only">View notifications</span>
                    <BellIcon className="h-6 w-6" aria-hidden="true" />
                  </button>*/}

                  {/* Profile dropdown */}
                  {/*<Menu as="div" className="relative ml-3">
                    <div>
                      <MenuButton className="relative flex rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2">
                        <span className="absolute -inset-1.5" />
                        <span className="sr-only">Open user menu</span>
                        <img
                          className="h-8 w-8 rounded-full"
                          src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                          alt=""
                        />
                      </MenuButton>
                    </div>
                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-200"
                      enterFrom="transform opacity-0 scale-95"
                      enterTo="transform opacity-100 scale-100"
                      leave="transition ease-in duration-75"
                      leaveFrom="transform opacity-100 scale-100"
                      leaveTo="transform opacity-0 scale-95"
                    >
                      <MenuItems className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <MenuItem>
                          {({ focus }) => (
                            <a
                              href="#"
                              className={`
                                block px-4 py-2 text-sm text-gray-700
                                ${focus ? 'bg-gray-100' : ''}
                              `}
                            >
                              Your Profile
                            </a>
                          )}
                        </MenuItem>
                        <MenuItem>
                          {({ focus }) => (
                            <a
                              href="#"
                              className={`
                                block px-4 py-2 text-sm text-gray-700
                                ${focus ? 'bg-gray-100' : ''}
                              `}
                            >
                              Settings
                            </a>
                          )}
                        </MenuItem>
                        <MenuItem>
                          {({ focus }) => (
                            <a
                              href="#"
                              className={`
                                block px-4 py-2 text-sm text-gray-700
                                ${focus ? 'bg-gray-100' : ''}
                              `}
                            >
                              Sign out
                            </a>
                          )}
                        </MenuItem>
                      </MenuItems>
                    </Transition>
                  </Menu>*/}

                  {/* Language selection */}
                  <Menu as="div" className="relative ml-3">
                    <div>
                      <MenuButton className="relative flex rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2">
                        <span className="absolute -inset-1.5" />
                        <span className="sr-only">Open language menu</span>
                        <GlobeAltIcon className="h-6 w-6" aria-hidden="true" />
                      </MenuButton>
                    </div>
                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-200"
                      enterFrom="transform opacity-0 scale-95"
                      enterTo="transform opacity-100 scale-100"
                      leave="transition ease-in duration-75"
                      leaveFrom="transform opacity-100 scale-100"
                      leaveTo="transform opacity-0 scale-95"
                    >
                      <MenuItems className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        {possibleLngs.map((lng) => (
                          <MenuItem key={lng}>
                            {({ focus }) => (
                              <a
                                href="#"
                                className={`
                                  block px-4 py-2 text-sm text-gray-700
                                  ${focus ? 'bg-gray-100' : ''}
                                  ${getCurrentLng() === lng ? 'font-bold' : ''}
                                `}
                                onClick={() => {
                                  void i18nRoot.changeLanguage(lng);
                                }}
                              >
                                {lngs[lng].nativeName}
                              </a>
                            )}
                          </MenuItem>
                        ))}
                      </MenuItems>
                    </Transition>
                  </Menu>
                </div>
              </div>
            </div>

            <DisclosurePanel className="menu:hidden">
              <div className="space-y-1 pb-4 pt-2">
                {Object.values(links).map((r) => {
                  return (
                    <Link key={r.path} to={r.path} tabIndex={-1}>
                      <DisclosureButton
                        className={
                          path === r.path
                            ? 'block border-l-4 border-brand-500 bg-brand-50 py-2 pl-3 pr-4 text-base font-medium text-brand-700'
                            : 'block border-l-4 border-transparent py-2 pl-3 pr-4 text-base font-medium text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700'
                        }
                        style={{ width: '-webkit-fill-available' }}
                      >
                        {t(r.title)}
                      </DisclosureButton>
                    </Link>
                  );
                })}
              </div>
            </DisclosurePanel>
          </>
        )}
      </Disclosure>

      <div className="p-3">
        <Outlet />
      </div>
      <Toaster expand={true} richColors closeButton />
    </>
  );
}

export default App;
