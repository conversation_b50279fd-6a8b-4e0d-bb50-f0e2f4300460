import {
  BaseErrorDto,
  user,
  uuidFormat,
  reservation,
  Timestamps,
  getBackendLng,
  balance,
} from '@s4nt14go/book-backend';
import { ApolloError } from '@apollo/client/errors';
import { getCurrentLng } from '../../trans/i18n.ts';
import { clone, Spread } from '../../utils.tsx';
import { toast } from 'sonner';
import { CommonTrans } from '../../trans/Common.trans.ts';
import { handleDataReceived, handleError, Status } from './storeUtils.ts';
import { client } from '../../apolloClient.ts';
import {
  applyGetBusinessUser,
  GET_BUSINESS_USER,
} from '../user/getBusinessUser.ts';
import {
  readStore as UserReadStore,
  setStore as UserSetStore,
} from '../user/store/userStore.ts';
import { SortingState } from '@tanstack/react-table';
import {
  ColumnsDisplayedKeys as MCustomerColumnsDisplayedKeys,
} from '../../pages/customers/mcustomer/table/core.ts';
import {
  ColumnsDisplayedKeys as ReservationColumnsDisplayedKeys,
} from '../../pages/reservations/table/core.ts';
import {
  ColumnsDisplayedKeys as BalanceColumnsDisplayedKeys,
} from '../../pages/balance/table/core.ts';

type ManuallyCreatedCustomerDtoWithTimestamps =
  user.ManuallyCreatedCustomerDtoWithTimestamps;
type ReservationOptionDto = reservation.ReservationOptionDto;
type ReservationOptionWithAvailabilityDto =
  reservation.ReservationOptionWithAvailabilityDto;
type ReservationDto = reservation.ReservationDtoWithTimestamps;
type BalanceRowDtoWithTimestamps = balance.BalanceRowDtoWithTimestamps;

type ReservationOptionDtoWithTimestamps = ReservationOptionDto & Timestamps;
export type ReservationOptionWithAvailabilityAndVisible =
  ReservationOptionWithAvailabilityDto & { visible: boolean }; // visible is only relevant to the desktop/wide schedule view, since the narrow/mobile view doesn't have the option to hide columns, there is just a select to choose only one service.
export type AppStateData = {
  userId: null | string;
  calendar: {
    availabilityCalendar: null | Calendar;
    reservationsCalendar: null | Calendar;
  };
  reservation: {
    reservations: ReservationDto[] | null;
    reservationsVisible: Record<ReservationColumnsDisplayedKeys, boolean>;
    reservationsPageSize: number;
    reservationsSorting: SortingState;
    reservationsShowCancelled: boolean;
    reservationOptions: ReservationOptionDtoWithTimestamps[] | null;
    reservationOptionsWithAvailability: // Actually it means reservation options with availability info but not that they are available, e.g. ReservationOption.availabilityGot can be null
      | ReservationOptionWithAvailabilityAndVisible[]
      | null;
    profile: {
      title: null | string;
      welcome: null | string;
      slug: null | string;
    };
    gs: {
      cancellationUpTo: null | number;
      phoneRequired: null | boolean;
      maxDaysAhead: null | number;
      minTimeBeforeService: null | number;
      maxSimultaneousReservations: null | number;
      reminderTemplate: null | string;
    };
  };
  balance: {
    current: null | number;
    rows: null | BalanceRowDtoWithTimestamps[];
    rowsVisible: Record<BalanceColumnsDisplayedKeys, boolean>;
    rowsPageSize: number;
  }
  user: {
    user: null | user.BusinessUserForFrontend;
    manuallyCreatedCustomers: ManuallyCreatedCustomerDtoWithTimestamps[] | null;
    manuallyCreatedCustomersVisible: Record<MCustomerColumnsDisplayedKeys, boolean>;
    manuallyCreatedCustomersPageSize: number;
    manuallyCreatedCustomersSorting: SortingState;
  };
};
export type AppStateWithUserId = Spread<AppStateData, { userId: string }>;
export type Calendar = {
  id: string;
  name: string;
};
export const AppStateNull: () => AppStateData = () => (clone({
  userId: null,
  calendar: {
    availabilityCalendar: null,
    reservationsCalendar: null,
  },
  balance: {
    current: null,
    rows: null,
    rowsVisible: {
      balance: true,
      description: true,
      change: true,
      date: true,
      customerId: false,
      reservationId: false,
    },
    rowsPageSize: 10,
  },
  reservation: {
    reservations: null,
    reservationsVisible: {
      id: false,
      fullName: true,
      start: true,
      service: true,
      location: true,
      created_at: true,
      customerType: false,
      cancel: true,
      customerId: false,
      reminder: true,
    },
    reservationsPageSize: 10,
    reservationsSorting: [
      {
        id: 'start',
        desc: true,
      },
    ],
    reservationsShowCancelled: true,
    reservationOptions: null,
    reservationOptionsWithAvailability: null,
    profile: {
      title: null,
      welcome: null,
      slug: null,
    },
    gs: {
      cancellationUpTo: null,
      phoneRequired: null,
      maxDaysAhead: null,
      minTimeBeforeService: null,
      maxSimultaneousReservations: null,
      reminderTemplate: null,
    },
  },
  user: {
    user: null,
    manuallyCreatedCustomers: null,
    manuallyCreatedCustomersVisible: {
      firstName: true,
      lastName: true,
      notes: true,
      created_at: true,
      updated_at: true,
      actions: true,
      id: false,
    },
    manuallyCreatedCustomersPageSize: 10,
    manuallyCreatedCustomersSorting: [
      {
        id: 'updated_at',
        desc: true,
      },
    ],
  },
}));

export type Apply<Data> = (args: {
  appState: AppStateData;
  data: Data | null;
  apolloError: ApolloError | null;
  errors: null | [BaseErrorDto, ...BaseErrorDto[]];
  status: Status.ERROR_RECEIVED | Status.DATA_RECEIVED; // Apply isn't called for 'LAUNCHED'
}) => AppStateData;

export const LSitem = 'appState';

export class AppState {
  private static state: AppStateData;
  private static localStorage: Storage;

  public constructor(args: { initialState: AppStateData; localStorage: Storage }) {
    const { initialState, localStorage } = args;
    AppState.state = initialState;
    AppState.localStorage = localStorage;
  }

  static applyState(newState: AppStateData) {
    AppState.state = newState;
    const stringified = JSON.stringify(newState);

    // Without limiting the quantity of retrieved possible times to reserve, the size exceeded the localStorage. Once I put the limit of 20.000 maximum times to reserve, it doesn't fail, but I leave the code for now.
    try {
      AppState.localStorage.setItem(LSitem, stringified); // Here is where appState is saved to localStorage.
    } catch (e) {
      console.error(
        `Error when trying to set ${stringified.length} characters in localStorage, maybe your availability times are excessive`,
        e,
      );
      console.log(`Will try saving without availability`);
      const noAvail = JSON.parse(stringified) as AppStateData;
      noAvail.reservation.reservationOptionsWithAvailability = null;
      const noAvailStringified = JSON.stringify(noAvail);
      try {
        AppState.localStorage.setItem(LSitem, noAvailStringified);
      } catch (e) {
        console.error(
          `Error when trying to set ${stringified.length} characters in localStorage.`,
          e,
        );
      }
    }
  }

  static setResOptVisibility(args: {
    // Reservation option visibility in Schedule.tsx
    reservationOptionsWithAvailabilityId: string;
    visible: boolean;
  }): ReservationOptionWithAvailabilityAndVisible[] | null {
    const { reservationOptionsWithAvailabilityId, visible } = args;
    const idx =
      AppState.state.reservation.reservationOptionsWithAvailability!.findIndex(
        (r) => r.id === reservationOptionsWithAvailabilityId,
      );

    const copy = clone(AppState.state); // Si no hago una copia da error "Cannot assign to read only property 'visible'"
    if (idx !== -1)
      copy.reservation.reservationOptionsWithAvailability![idx].visible = visible;
    AppState.state = copy;

    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
    return AppState.state.reservation.reservationOptionsWithAvailability;
  }

  static setMCustomersVisibility = (args: { field: MCustomerColumnsDisplayedKeys; visible: boolean }) => {
    const { field, visible } = args;
    AppState.state.user.manuallyCreatedCustomersVisible[field] = visible;
    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  };

  static setMCustomersPageSize = (pageSize: number) => {
    AppState.state.user.manuallyCreatedCustomersPageSize = pageSize;
    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  };

  static setMCustomersSort = (sorting: SortingState) => {
    AppState.state.user.manuallyCreatedCustomersSorting = sorting;
    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  };

  static setReservationsVisibility = (args: {
    field: ReservationColumnsDisplayedKeys;
    visible: boolean;
  }) => {
    const { field, visible } = args;
    const copy = clone(AppState.state); // Si no hago una copia da error "Cannot assign to read only property"
    copy.reservation.reservationsVisible[field] = visible;
    AppState.state = copy;
    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  };

  static setReservationsPageSize = (pageSize: number) => {
    AppState.state.reservation.reservationsPageSize = pageSize;
    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  };

  static setReservationsSort = (sorting: SortingState) => {
    AppState.state.reservation.reservationsSorting = sorting;
    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  };

  static setReservationsShowCancelled = (show: boolean) => {
    AppState.state.reservation.reservationsShowCancelled = show;
    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  };

  static setBalanceRowsVisibility = (args: {
    field: BalanceColumnsDisplayedKeys;
    visible: boolean;
  }) => {
    const { field, visible } = args;
    AppState.state.balance.rowsVisible[field] = visible;
    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  };

  static setBalanceRowsPageSize = (pageSize: number) => {
    AppState.state.balance.rowsPageSize = pageSize;
    AppState.localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  };

  static getState() {
    return AppState.state;
    // return JSON.parse(localStorage.getItem(LSitem)!);  // For testing purposes if I want to manipule AppState.state
  }

  static reset(userId: string) {
    AppState.state = {
      ...AppStateNull(),
      userId,
    };
    localStorage.setItem(LSitem, JSON.stringify(AppState.state));
  }
}

export function createAppState(args: {
  initialState: AppStateData;
  localStorage: Storage;
}) {
  const { initialState, localStorage } = args;
  new AppState({
    initialState,
    localStorage,
  });
}

export function getInitialStateFromLocal(args: { localStorage: Storage }) {
  // console.log('getInitialStateFromLocal');
  const { localStorage } = args;
  let state: AppStateData | null = null;
  const appStateLS = localStorage.getItem(LSitem);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let appStateParsed: any;
  if (appStateLS) {
    try {
      appStateParsed = JSON.parse(appStateLS);
      // console.log({ appStateParsed });
      if (appStateParsed && isUUid(appStateParsed.userId)) {
        state = mergeDeep(AppStateNull(), appStateParsed);
      }
    } catch (error) {
      console.error('Error parsing appState from localStorage', error);
    }
  }

  if (!state) {
    state = AppStateNull();
    localStorage.removeItem(LSitem);
  }

  // console.log({ state });
  return {
    stateFromLocal: state,
  };
}

export async function getInitialStateFromNetwork(userId: string) {
  // console.log('getInitialStateFromNetwork', userId);

    let requestError = false;
    try {
      const response = await client.query({
        query: GET_BUSINESS_USER,
        variables: {
          id: userId,
        },
      });
      handleDataReceived({
        response,
        requestName: 'getBusinessUser',
        queryName: 'getBusinessUser',
        setStore: UserSetStore,
        readStore: UserReadStore,
        apply: applyGetBusinessUser,
      });
    } catch (e) {
      handleError({
        e,
        requestName: 'getBusinessUser',
        setStore: UserSetStore,
        readStore: UserReadStore,
        apply: applyGetBusinessUser,
      });
      requestError = true;
    }

    if (requestError)
      alert('Error de inicialización al intentar obtener datos de la red');
}

export function checkStatusIsReceived<Response>({
  data,
  status,
}: {
  data: unknown;
  status: Status | null;
}) {
  if (!(status === Status.DATA_RECEIVED || status === Status.ERROR_RECEIVED)) {
    const msg = `Status ${status} isn't one of the possible ones: DATA_RECEIVED or ERROR_RECEIVED`;
    console.log(msg, data);
    throw Error(msg);
  }
  return {
    data: data as Response,
    status,
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isUUid(test: any): test is string {
  return typeof test === 'string' && test !== '' && uuidFormat.test(test);
}

function isObject(item: unknown): boolean {
  return (item && typeof item === 'object' && !Array.isArray(item)) as boolean;
}

// Deep merge two objects.
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function mergeDeep(base: any, ...update: any[]) {
  if (!update.length) return base;
  const source = update.shift();

  if (isObject(base) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!base[key]) Object.assign(base, { [key]: {} });
        mergeDeep(base[key], source[key]);
      } else {
        Object.assign(base, { [key]: source[key] });
      }
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
  return mergeDeep(base, ...update);
}

const commonTrans = new CommonTrans();

export function handleUnexpectedError(data: {
  requestName: string;
  args: unknown;
}) {
  const { requestName, args } = data;
  const backLng = getBackendLng(getCurrentLng());
  const msg = commonTrans.resources[backLng].default.requestError(requestName);
  console.log(msg, args);
  toast.error(msg);
}
