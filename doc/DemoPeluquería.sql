-- Insert reservation options for hairdresser example
-- Run script on DB<PERSON>ver. For an online uuid generator, visit https://www.uuidgenerator.net/version4
INSERT INTO public.business_users (id, "firstName", "lastName", email, phone, timezone, created_at, updated_at, deleted_at, lng, "phoneVerificationAttempts", "phoneVerificationBlockedUntil", "phoneVerificationCode", "phoneVerificationCodeExpiresAt", "phoneVerificationNewPhone", "phoneVerificationStatus", "emailVerificationStatus", "emailVerificationAttempts", "emailVerificationBlockedUntil", "emailVerificationNewEmail", "emailVerificationCode", "emailVerificationCodeExpiresAt") values
  ('470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid, 'Juan', 'Martinez', '<EMAIL>', '+************', 'America/Cordoba', '2024-02-24 00:00:00.000', '2025-06-05 05:23:08.849', NULL, 'es', '{"2024-02-26 00:00:00+00","2025-06-05 05:22:00+00"}', NULL, 9418, '2025-06-05 05:23:42.000', '+************', 'SUCCESS', 'SUCCESS', '{"2024-02-27 00:00:00+00"}', NULL, '<EMAIL>', 5678, '2024-02-27 00:01:00.000');

  INSERT INTO public.calendars (id, "userId", "name", "type", created_at, updated_at) VALUES
    ('<EMAIL>', '470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid, 'disponibilidad', 'A', '2025-06-05 15:48:44.264', '2025-06-05 15:48:44.264'),
    ('<EMAIL>', '470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid, 'reservas', 'R', '2025-06-05 15:49:18.251', '2025-06-05 15:49:18.251');

INSERT INTO public.reservation_options ("userId",id,"name",active,"srvName","srvDescription","locName","locDescription","locIdentifier","locShowInfoWhenReserving","trHour","trMinute","trZone","noteWhile","noteAfter",created_at,updated_at,version,"every","srvDuration") VALUES
	('470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid,'e4aee74e-a9c4-4e86-b590-90f8b39e57d5'::uuid,'Corte de pelo ✂️',true,'Corte de pelo ✂️',null,'silla',null,'silla',false,0,0,'America/Cordoba',null,null,'2024-02-23 10:47:11.644','2024-02-23 10:47:11.644',1,30,30),
	('470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid,'b2095b43-70e8-4b9d-b66c-2a032801320c'::uuid,'Barba 🧔',true,'Barba 🧔'                ,null,'silla',null,'silla',false,0,0,'America/Cordoba',null,null,'2024-02-23 10:47:11.644','2024-02-23 10:47:11.644',1,15,15),
	('470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid,'2a9d022f-ce9b-456b-ba04-4452c3009935'::uuid,'Corte + barba 🔥',true,'Corte + barba 🔥',null,'silla',null,'silla',false,0,0,'America/Cordoba',null,null,'2024-02-23 10:47:11.644','2024-02-23 10:47:11.644',1,45,30);

INSERT INTO public.gs_for_reservations (id, "cancellationUpTo", title, welcome, slug, created_at, updated_at, "phoneRequired", "maxSimultaneousReservations", "resLimMaxDaysAhead", "resLimMinTimeBeforeService", "reminderTemplate") VALUES
  ('470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid, 120, 'Barbería Don Juan', '¡Bienvenido! ¡Gracias por elegirnos!
Nos encontramos en Av. Santa Fé 1547
Clickear en el horario para hacer una reserva.', 'donjuan', '2024-02-24 00:00:00.000', '2025-06-05 05:20:31.880', true, 2, 30, 0, 'Hola {firstName}! Este es un recordatorio de tu reserva para {service} el {day} a las {time}. ¿Me confirmas que vienes?');

INSERT INTO public.manually_created_customers (id, "firstName", "lastName", "createdBy", created_at, updated_at, deleted_at, notes, "fullName") VALUES
  ('fd1b931f-aee9-49fc-8092-db248c7836d3'::uuid, 'Diego', 'Fernandez', '470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid, '2024-02-26 00:00:00.000', '2025-06-05 05:27:15.620', NULL, NULL, 'Diego Fernandez'),
  ('0dbc272f-4871-486e-854f-e55f1eca1d8d'::uuid, 'Roberto', 'Sanchez', '470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid, '2024-02-28 00:00:00.000', '2025-06-05 05:27:23.835', NULL, NULL, 'Roberto Sanchez'),
  ('d654a741-ac0e-44bd-b5f4-05929f278f91'::uuid, 'Héctor', 'López', '470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid, '2024-03-01 00:00:00.000', '2025-06-05 05:28:36.937', NULL, NULL, 'Héctor López'),
  ('3be6adcc-f31a-4077-8e01-5d525c59e0ab'::uuid, 'Luciano', 'Ramirez', '470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid, '2024-03-01 00:00:00.000', '2025-06-05 05:29:12.542', NULL, NULL, 'Luciano Ramirez');

INSERT INTO public.balance_rows ("userId", "index", balance, movement, "change", created_at, updated_at, "reservationId", "customerFullName", "srvName", "customerId", "date") VALUES
  ('470a39fb-3e24-4868-8d11-4c1a6621d78b'::uuid, 0, 50, 'INITIAL_FREE_BALANCE', 50, '2025-06-05 00:00:00.000', '2025-06-05 00:00:00.000', NULL, NULL, NULL, NULL, '2025-06-05 00:00:00.000');

-- Setear en el browser:
-- localStorage.setItem('appState', JSON.stringify({ userId: '470a39fb-3e24-4868-8d11-4c1a6621d78b' }))