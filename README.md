# Frontend de turnero

## Selección de la versión de node

Como voy a utilizar el package con el código de dominio del backend, usar la misma versión de node que la utlizada en el backend, que para el turnero es la 20.

## Proyecto de partida para el frontend

Como en principio voy a usar [React Router](https://reactrouter.com) y [Apollo Client](https://www.apollographql.com/docs/react/), y veo que ambos en sus tutos usan [Vite](https://vitejs.dev), lo uso como build tool. También la última release 5.0.1 de [create-react-app](https://github.com/facebook/create-react-app) fue el 12/4/22, mientras que [Vite](https://github.com/vitejs/vite/releases) (si bien involucra varios frontends y no sólo react) sacó la última versión 5.2.11 hace 1 semana.

```shell
npm create vite@latest turnero-front -- --template react-ts
```

En el [README.md](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts) generado por Vite, se mencionaban 2 plugins oficiales (`@vitejs/plugin-react` y `@vitejs/plugin-react-swc`), sin embargo decidí no instalar ninguno porque no estaba seguro de sus ventajas.

Lo que sí hice fue configurar lo recomendado para apps de producción en `.eslintrc.cjs`.

Modifiqué los scripts de `package.json`:
```json5
"scripts": {
"dev": "vite",
"typecheck": "tsc --noEmit",
"lint": "eslint . --ext ts,tsx --max-warnings 0",
"build": "vite build",
"preview": "npm run build && vite preview",
}
```

## TailwindCSS

[Instalación](https://tailwindcss.com/docs/guides/vite) de TailwindCSS en Vite.

```shell
# Install tailwindcss and its peer dependencies
npm install -D tailwindcss postcss autoprefixer
# Generate tailwind.config.js and postcss.config.js
npx tailwindcss init -p
```

Add the paths to all of your template files in your `tailwind.config.js` file:
```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
};
```

Una cosa que no salía en las instrucciones es que también es necesario agregar `tailwindcss` en `vite.config.js`:
```javascript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "tailwindcss";

// https://vitejs.dev/config/
export default defineConfig({
  css: {
    postcss: {
      plugins: [tailwindcss()],
    },
  },
});
```

### Tips

Para ver los valores de default de TailwindCSS:
```shell
npx tailwindcss init tailwind-full.config.js --full
```

[Screen breakpoints](https://tailwindcss.com/docs/responsive-design): `sm: 640px`, `md: 768px`, `lg: 1024px`, `xl: 1280px`, `2xl: 1536px`. Mi celu TCL408 tiene 720x1612px, por lo que mientras se vea bien en `640px` está ok.

Ejemplo de customización de colores en `tailwind.config.js`:
```javascript
/** @type {import('tailwindcss').Config} */

// Para desarrollo está bueno que se incluyan todas las clases porque de esa forma puedo agregarlas dinámicamente en el browser, eso se logra poniendo "pattern: /.*/" en la sefelist. Sin embargo, si incluyo todas las clases para todos los colores demora mucho en actualizarse la página, por lo que excluyo todos los colores excepto fuchsia y gray.
const safelist = [];
if (process.env.NODE_ENV === 'development') {
  console.log('Including many Tailwind classes for development');
  safelist.push({
    // pattern: /.*/
    // Estos son todos los colores según https://tailwindcss.com/docs/customizing-colors
    // slate|gray|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|fuchsia|pink|rose
    // Dejo sólo fuchsia y gray sin incluir, es decir, todas las posibilidades de fuchsia van a ser generadas
    pattern: /^(?!.*\b(slate|zinc|neutral|stone|red|orange|amber|yellow|lime|green|emerald|teal|cyan|sky|blue|indigo|violet|purple|pink|rose)\b).+$/
  });
}

export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        brand: {
          50: "var(--brand-50)",
          100: "var(--brand-100)",
          200: "var(--brand-200)",
          300: "var(--brand-300)",
          400: "var(--brand-400)",
          DEFAULT: "rgb(var(--brand) / <alpha-value>)",
          500: "var(--brand-500)",
          600: "var(--brand-600)",
          700: "var(--brand-700)",
          800: "var(--brand-800)",
          900: "var(--brand-900)",
          950: "var(--brand-950)",
        },
      },
      fontFamily: {
        poppins: ['Poppins', 'sans-serif'],  // Agregando al index.html la fuente: https://github.com/tailwindlabs/tailwindcss-from-zero-to-production/blob/main/08-optimizing-for-production/index.html#L8 (de todas formas seguir las instrucciones de https://fonts.google.com/)
      },
    },
  },
  variants: {
    extend: {
      backgroundColor: ["active"],  // Para que los botones cambien de color al hacer click, ejemplo: https://github.com/tailwindlabs/tailwindcss-from-zero-to-production/blob/main/08-optimizing-for-production/src/App.jsx#L28
    },
  },
  plugins: [],
  safelist,
}
```

Agregar al `body` del `index.html` la font:
```html
<body class="font-poppins">
```

`index.css`:
```css
:root {
    --mantis-50: #f6faf3;
    --mantis-100: #e9f5e3;
    --mantis-200: #d3eac8;
    --mantis-300: #afd89d;
    --mantis-400: #82bd69;
    --mantis: rgb(130,189,105);
    --mantis: 130 189 105;
    --mantis-500: #61a146;
    --mantis-600: #4c8435;
    --mantis-700: #3d692c;
    --mantis-800: #345427;
    --mantis-900: #2b4522;
    --mantis-950: #13250e;

    --brand-50: var(--mantis-50);
    --brand-100: var(--mantis-100);
    --brand-200: var(--mantis-200);
    --brand-300: var(--mantis-300);
    --brand-400: var(--mantis-400);
    --brand: var(--mantis);
    --brand-500: var(--mantis-500);
    --brand-600: var(--mantis-600);
    --brand-700: var(--mantis-700);
    --brand-800: var(--mantis-800);
    --brand-900: var(--mantis-900);
    --brand-950: var(--mantis-950);
}

@tailwind base;
@tailwind components;
@tailwind utilities;
```
Esta customización me permite usar por ejemplo `border-brand-500` o el color más el alpha `text-brand/50`.

[Ejemplo](https://github.com/s4nt14go/turnero-front/commit/4622bcfaa5d937c64b04934d8f6b4e5807b91e08) de cómo hacer alias a una clase de Tailwind (uso `bg-danger-500` en lugar de `bg-red-500` en delete button, de manera que si el día de mañana quiero cambiar el color de esos botones, es más sencillo).

[Paleta](https://tailwindcss.com/docs/colors) de colores que incluye Tailwind. Para generar la paleta [UI Colors](https://uicolors.app/create) y para convertir de hexa a rgb [Convert a Color](https://www.convertacolor.com). Si voy a elegir un segundo color, [aquí](https://in-your-saas.github.io/material-ui-theme-editor) puedo ver cómo queda.

Este [commit](https://github.com/s4nt14go/turnero-front/tree/e72a8c8b117da78c3accb8df99fa1b1e09caf9fd) puede servir como base para empezar un proyecto.

[Commit](https://github.com/s4nt14go/turnero-front/commit/13bd4cd8e946a453b9ad0db2a8db8ff8aac33bee) ejemplo al hacer branding: cambio de logo, color `brand`, título y descripción en el `index.html`.

En [Tailwind Cheat Sheet](https://nerdcave.com/tailwind-cheat-sheet) se pueden buscar clases, aunque es de la v2. También se puede buscar en [sitio oficial](https://tailwindcss.com/docs).

Para deshabilitar algún elemento, aplicar estas clases:
```jsx
const disabledClasses = 'opacity-50 cursor-not-allowed outline-none';
```
`outline-none` debe ir sin `data-[disabled]:` adelante para que no se muestre el borde azul con `:focus-visible` por lo que no es posible aplicar estas clases valiéndose de ese atributo `disabled`, ver [ejemplo](https://github.com/s4nt14go/turnero-front/blob/02eb606a7c61ef27a20df968a98a0400844aeab4/src/pages/Connection.tsx#L210).

Agrego algunos videos del [canal de YouTube](https://www.youtube.com/@TailwindLabs/videos) que me pueden llegar a ser útiles para el proyecto.

[Theming Tailwind with CSS Variables](https://www.youtube.com/watch?v=MAtaT8BZEAo) y [código](https://play.tailwindcss.com/YelhilBeHb). UPDATE v3.1: [Simplified CSS variable color configuration](https://www.youtube.com/watch?v=nOQyWbPO2Ds&t=540s)<sup>2</sup> <br />
[Print modifier](https://www.youtube.com/watch?v=mSC6GwizOag&t=316s)<sup>1</sup> <br />
[Scroll behavior](https://www.youtube.com/watch?v=mSC6GwizOag&t=733s)<sup>1</sup> <br />
[Multi-column layout](https://www.youtube.com/watch?v=mSC6GwizOag&t=856s)<sup>1</sup>: columnas responsive <br />
[Styling open/closed states](https://www.youtube.com/watch?v=mSC6GwizOag&t=1143s)<sup>1</sup>: para las explicaciones de turnero <br />
[Fancy underlines](https://www.youtube.com/watch?v=mSC6GwizOag&t=1208s)<sup>1</sup> <br />
[Border spacing utilities](https://www.youtube.com/watch?v=nOQyWbPO2Ds&t=774s)<sup>2</sup>: bottom border del encabezado de tabla <br />
[Enabled and optional variants](https://www.youtube.com/watch?v=nOQyWbPO2Ds&t=900s)<sup>2</sup>: button disabled e indicación de input required <br />
[Dialog backdrop variant](https://www.youtube.com/watch?v=nOQyWbPO2Ds&t=1229s)<sup>2</sup> <br />

Referencias:
1. [What's new in Tailwind CSS v3.0?](https://www.youtube.com/watch?v=mSC6GwizOag)
2. [What's new in Tailwind CSS v3.1?](https://www.youtube.com/watch?v=nOQyWbPO2Ds)

Otros sitios con más componentes:
- https://tailwindui.com/components
- https://flowbite-react.com
- https://preline.co/docs/accordion.html
- https://tailwindflex.com
- https://ui-component-library.netlify.app
- https://kimia-ui.vercel.app/components/accordion
- https://github.com/search?q=tailwind&type=repositories&s=stars&o=desc

#### screenshot-to-code

[screenshot-to-code](https://github.com/abi/screenshot-to-code) genera código de React + Tailwind. También se puede editar el código en un Codepen que genera, así como pedirle que haga modificaciones.

Sin embargo, al probar subir una screenshot para crear una nueva sección a partir de mi figma si bien genera algo, no me resultó tan útil, también en mi proyecto uso la librería `@headlessui/react` que no usa `screen-to-code`, así que me parece que lo mejor es hacerlo manualmente viendo los componentes de [HeadlessUI](https://headlessui.com).

De todos modos, lo cloné en `/Users/<USER>/desarrollo/react/tailwind/01screenshot-to-code`. Hacer un update del project para pullear nuevos commits del repo (esto es especialmente importante sin se actualizan los modelos utilizados).

Instrucciones para ejecutarlo localmente sacadas del [README.md](https://github.com/abi/screenshot-to-code?tab=readme-ov-file#-getting-started). Primero copiar mis keys para ChatGPT y Claude 3.5 Sonnet en `backend/.env`:
```dotenv
OPENAI_API_KEY=********************************************************
ANTHROPIC_API_KEY=************************************************************************************************************
```
Ejecutar backend:
```shell
cd /Users/<USER>/desarrollo/react/tailwind/01screenshot-to-code/backend
poetry install
poetry shell
poetry run uvicorn main:app --reload --port 7001
```
Ejecutar frontend:
```shell
cd /Users/<USER>/desarrollo/react/tailwind/01screenshot-to-code/frontend
yarn dev
```

### Forms

Hay un plugin para forms [@tailwindcss/forms](https://github.com/tailwindlabs/tailwindcss-forms) ([Video tutorial](https://www.youtube.com/watch?v=pONeWAzDsQg)). Sin embargo, me parece más conveniente [HeadlessUI](https://headlessui.com) que maneja los estados de cada input, integrándolo en React con funcionalidad.

Instalación de HeadlessUI:
```shell
npm install @headlessui/react
```

[Building a Custom Dropdown Menu with Headless UI React and Tailwind CSS](https://www.youtube.com/watch?v=qJnIJa-cF2M). <br />
[Building Accessible Switch/Toggle Buttons with Headless UI React and Tailwind CSS](https://www.youtube.com/watch?v=3uzhz0Q_vUg).

### Components

[tailwindir.com](https://www.tailwindir.com/components) tiene un montón de components free para usar. Acá pongo algunos pero hay muchos más:

- [navbars](https://www.tailwindir.com/components/navbars)
- [tables](https://www.tailwindir.com/components/tables)
- [modals](https://www.tailwindir.com/components/modals)
- [notifications](https://www.tailwindir.com/components/notifications)
- [dividers](https://www.tailwindir.com/components/dividers)
- [input-groups](https://www.tailwindir.com/components/input-groups)
- [alerts](https://www.tailwindir.com/components/alerts)
- [checkboxes](https://www.tailwindir.com/components/checkboxes)
- [sign-in-forms](https://www.tailwindir.com/components/sign-in-forms)
- [steps](https://www.tailwindir.com/components/steps)
- [footers](https://www.tailwindir.com/components/footers)
- [pagination](https://www.tailwindir.com/components/pagination)
- [calendars](https://www.tailwindir.com/components/calendars)
- [404-pages](https://www.tailwindir.com/components/404-pages)

Componentes sencillos, tal vez conviene fijarse en [HeadlessUI](https://headlessui.com) directamente:
- [toggles](https://www.tailwindir.com/components/toggles)
- [stats](https://www.tailwindir.com/components/stats)
- [dropdowns](https://www.tailwindir.com/components/dropdowns)
- [textareas](https://www.tailwindir.com/components/textareas)
- [radio-groups](https://www.tailwindir.com/components/radio-groups)

Para arrancar el layout/navegación de la app fijarse en [navbars](https://www.tailwindir.com/components/navbars).

### Modals

Usar como base ejemplo [LocationInfo.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/configure/21LocationInfo.tsx) reparando en los comentarios acerca de `sm:flex` y `max-w-lg`. Para botones que se deshabilitan si un request está en flight ver los modales de desconexión de calendarios de [Connection.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/connection/Connection.tsx).

### Caveats

Si uso nombre de clases dinámicas poner las clases completas como [aquí](https://github.com/tailwindlabs/tailwindcss-from-zero-to-production/blob/main/08-optimizing-for-production/src/components/DestinationCard.jsx#L4) ya que si pongo así:
```jsx
<img className={`h-${size} w-${size} flex-shrink-0`}>
```
...al construir el build de producción, el purge no va a encontrar esas clases y las va a eliminar ([fuente](https://youtu.be/HZn2LtBT59w?list=PL5f_mz_zU5eXWYDXHUDOLBE0scnuJofO0&t=8m33s)).

UPDATE: En v3 se cambió el compilador de purge a jit y probablemente esto ya no sea un problema.

### Otros videos no tan útiles (o relevantes para el proyecto turnero) pero que pueden servir
[How to Use Custom Fonts with Tailwind CSS](https://www.youtube.com/watch?v=sOnBG2wUm1s). <br />
[Translating a Custom Design System to Tailwind CSS](https://www.youtube.com/watch?v=cZc4Jn5nK3k). <br />
[Accent color & file inputs](https://www.youtube.com/watch?v=mSC6GwizOag&t=1016s)<sup>1</sup> <br />

## React conditional rendering with `&&`

<p align="center">
    <img src="doc/conditionalRendering.png" />
</p>

> Otra forma de transformar un 0 en booleano es mediante `!!` 

## Prettier

En la documentación de React simplemente [recomienda](https://react.dev/learn/editor-setup#formatting) agregar `eslint-config-prettier`, mientras que [vite](https://vitejs.dev) no lo menciona. Así que usé este [blog](https://medium.com/@nedopaka/setup-a-react-vite-project-with-prettier-vitest-2024-12e291669b4b) como base para instalarlo.

```shell
npm i -D prettier eslint-config-prettier eslint-plugin-prettier eslint-plugin-import
```

Copié `.prettierrc` del proyecto del back:
```json5
{
  "semi": true,
  "singleQuote": true,
  "printWidth": 83
}
```

Modifiqué `.eslintrc.cjs`:
```javascript
module.exports = {
  ...,
  extends: [
    ...,
  // This disables the formatting rules in ESLint that Prettier is going to be responsible for handling.
  // Make sure it's always the last config, so it gets the chance to override other configs.
  'eslint-config-prettier',
],
...,
plugins: ['react-refresh', 'import'],
  rules: {
...,
  'import/newline-after-import': ['error', { count: 1 }],
},
...
}
```

Agregué a los scripts del `package.json`:
```json5
{
  ...
  "scripts": {`
  "prettier-check": "prettier --config .prettierrc './src' --check",
  "prettier-format": "prettier --config .prettierrc './src' --write"
},
...
}

```

## Zustand

Para debugging se usa [Redux](https://github.com/reduxjs/redux-devtools) (instalé otras extensiones en Chrome pero no funcionaban). Se debe usar el middleware [devtools](https://github.com/pmndrs/zustand?tab=readme-ov-file#redux-devtools).

## Configuración de debug en WebStorm

Para poder debuggear en WebStorm, poniendo breakpoints, configurar de esta manera:

<p align="center">
    <img src="doc/debug1.png" />
</p>
<p align="center">
    <img src="doc/debug2.png" />
</p>

Luego de configurarlo de esa forma, se genera sólo el "JavaScript debug":

<p align="center">
    <img src="doc/debug3.png" />
</p>

La primera vez me abrió un chrome como recién instalado, sin los plugins (sin [React Developer Tools](https://chromewebstore.google.com/detail/react-developer-tools/fmkadmapgofadopljbjfkapdkoienihi)), pero al sincronizar con mi cuenta de Google, se instalan todos los plugins.

A veces me ha funcionado seleccionado `dev` y otras `dev JavaScript` al clickear en el ícono de debugging.

## Apollo Client

### Explorar la API en Apollo Sandbox

Ir a https://studio.apollographql.com/sandbox, copiar la url (por ejemplo: https://6bc47rkc7zdifkjndukpb6cejy.appsync-api.us-east-1.amazonaws.com/graphql) y en la configuración agregar dentro de "Shared headers":
```dotenv
x-api-key: <api-key>
# Fijarse en .env
```

### Instalación

```shell
npm i @apollo/client graphql
```

De `@apollo/client` se puede importar `useQuery` y `useMutation` para hacer queries y mutations respectivamente. También es necesario crear el `ApolloClient` y envolver la app con `ApolloProvider`. Este es un commit en el que agregué Apollo Client que pude servir como [ejemplo](https://github.com/s4nt14go/turnero-front/commit/fb324ede29b5c120a1002517ad0c3af0cf53f9ae).

[Documentación](https://www.apollographql.com/docs/react)

### Codegen

Para generar los hooks de Apollo Client, se puede usar GraphQL Code Generator, que es un generador de código que toma un esquema GraphQL y genera código de cliente a partir de él.

Para instalarlo seguí la esta [guía](https://the-guild.dev/graphql/codegen/docs/guides/react-vue), sin embargo no instalé `typescript` porque ya lo tenía, ni `ts-node` ya que aparentemente no tiene efecto:
```bash
npm i -D @graphql-codegen/cli @graphql-codegen/client-preset @parcel/watcher
```
`@graphql-codegen/client-preset` gives us some React-specific configuration.

Estas dependencias van a modificar el resultado de ejecutar `npm run generate`, una vez configurado como se explica en los pasos siguientes.

Para que WebStorm autocompletara al escribir las queries al hacer "gql(\` \<query\> \`)" (y no lo marcara como error), necesité copiar desde el backend `schema.graphql` y `.graphql`, lo que incluí en el archivo `codegen.ts`. En un momento cree un archivo `.graphqlconfig` como dice esta respuesta de [stackoverflow](https://stackoverflow.com/questions/57777853/why-webstorm-show-errors-in-gql-query-inside-apollo-object-in-vue-component-or#answer-57778342), poniendo `"schemaPath": [".graphql", "schema.graphql"]`, pero creo que no hace falta (borrar este comentario si confirmo que no es necesario).

De todas formas, para copiar las queries completas es conveniente usar https://studio.apollographql.com/sandbox/explorer.

Copiar `codegen.ts` agregar en `package.json`:
```json
"scripts": {
"generate": "graphql-codegen"
}
```

Ejecutar `npm run generate` con `ignoreNoDocuments: true`, de esa forma voy a poder ver las queries y mutations detectadas dentro de `__generated__/graphql.ts` pero aún no voy a poder hacerlas.

De hecho, generar ese primer `__generated__/gql.ts` rompe la app de react, al menos tengo que poner una query en el proyecto aunque no la use para que no rompa la app, como en el ejemplo siguiente.

Para que al ejecutar `npm run generate`, genere las queries que serán usadas con `useQuery` y `useMutation`, primero hay que poner dentro del código algo como esto por ejemplo:
```typescript
// useGetCalendars.ts
import { gql } from "@apollo/client";
const GET_BUSINESS_USER = gql(`
query Query($id: ID!) {
  getBusinessUser(id: $id) {
    result {
      email
    }
    time
  }
}
`);
```

Ahora al ejecutar `npm run generate`, efectivamente se va a crear y exportar desde `__generated__/gql` lo necesario para poder usar `useQuery`.

Si quiero ver qué es lo que responde el backend puedo agregar este código:
```typescript
client.query({
  query: GET_BUSINESS_USER,
}).then(result => {
  console.log(result);
});
```

Cada vez que agregue o modifique una query usada en `src`, debo ejecutar:
```bash
npm run generate
```

Para agregar las queries por primera vez puedo usar [Apollo Sandbox](https://studio.apollographql.com/sandbox/).

Si bien puede parecer intimidante la creación del hook `useGetCalendars.ts` que hace uso del hook de Apollo `useLazyQuery` (o `useMutation` para el caso de `useCheckGetEventsAndSave.ts`) por los types:
```typescript
// useGetCalendars.ts
function useGetCalendars(args: { userId: string }): {
  result: {
    data: GetCalendarsQuery | undefined;
    pending: boolean;
    error: ApolloError | undefined;
  };
  execute: (
    options?: Partial<
      LazyQueryHookExecOptions<GetCalendarsQuery, GetCalendarsQueryVariables>
    >,
  ) => Promise<QueryResult<GetCalendarsQuery, GetCalendarsQueryVariables>>;
} {
  const [execute, { data, loading: pending, error }] = useLazyQuery(
    GET_CALENDARS,
    {
      variables: args,
    },
  );

  return { execute, result: { data, pending, error } };
}
```
Empezar poniendo `null` en aquellos lugares que no sé qué va:
```typescript
// useGetCalendars.ts
function useGetCalendars(args: { userId: string }): {
  result: {
    data: null; // <------------------------------------------------
    pending: boolean;
    error: ApolloError | undefined;
  };
  execute: (
    options?: null, // <--------------------------------------------
  ) => Promise<null>; // <------------------------------------------
} {
  const [execute, { data, loading: pending, error }] = useLazyQuery(
    GET_CALENDARS,
    {
      variables: args,
    },
  );

  return { execute, result: { data, pending, error } };
}
```
...y WebStorm va a marcar que los tipos no son correctos y ofrece autocompletar con los correctos (esto obviamente, después de haber incluído en el código `GET_BUSINESS_USER` y ejecutado `graphql-codegen`).

Modifiqué `.eslintrc.cjs`, acá hay algunos de los cambios pero ver el archivo porque lo seguí modificando:
```javascript
module.exports = {
  ...
    rules: {
...
  "@typescript-eslint/consistent-type-definitions": "off",  // allow using types instead of only interfaces
    "@typescript-eslint/no-unsafe-assignment": "off",  // silent errors showed in useMutation
},
...
"settings": {
  "react": {
    "version": "detect"
  }
},
}
```

Agregué el archivo `.prettierignore`
```prettier
/src/__generated__/
```

#### Uso de fragments

Para evitar repetir los mismos campos en las queries, crear un archivo `fragments.ts` para las entidades de los módulos. Su declaración es global y pueden ser utilizados en las queries. Ver [balance](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/balance/fragments.ts), [calendar](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/calendar/fragments.ts), [reservation](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/reservation/fragments.ts) y [user](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/user/fragments.ts).

#### Errores

Si recibo un error:
```text
Uncaught Invariant Violation: Must contain a query definition.
{
    "framesToPop": 1,
    "name": "Invariant Violation"
}
```
Es posible que haya escrito un request de tipo `mutation` tomando como base una `query`.

## Arquitectura

Parto del modelo inicial de `explicit-design`, clonado en `/Users/<USER>/desarrollo/arch/11explicit-design-propio` ([github](https://github.com/s4nt14go/explicit-design-propio)) [Artículo interesante](https://www.linkedin.com/feed/update/urn:li:ugcPost:7226536111483850752/) de Nik Sumeiko con un ejemplo de cómo usar Context API de React para lograr dependency injection

El resultado de algunos componentes, por ejemplo clickear en el botón "VERIFY & SAVE" de `Connection.tsx` que resulta en los estados de "request in flight" ⇨ "saved" | "error", tiene efecto en otros, por ejemplo cuando se está en "request in flight" quiero que se deshabilite el input donde se ingresa el calendar id. Es por ello que resuelvo comunicar esos estados relacionados con requests usando el store de `zustand` así están disponibles para todos los componentes.

Al arrancar la app, primero se inicializa el appState según lo que hay en `localStorage` y luego, se actualiza con datos de la red:
```typescript
const { stateFromLocal } = getInitialStateFromLocal({
  localStorage,
});
createAppState({
  initialState: stateFromLocal,
  localStorage,
});
await getInitialStateFromNetwork({
  state: stateFromLocal,
});
```
en [App.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/App.tsx)

En el store se ponen los estados finales (al recibir `DATA_RECEIVED`, `ERROR_RECEIVED`) y también el transitorio (al registrar `LAUNCHED`), mientras que en el appState sólo se computan los valores a partir de los estados finales (`DATA_RECEIVED`, `ERROR_RECEIVED`).

En los componentes, primero se muestra la información de appState, esto sucede rápidamente ya que no se requieren requests, es información ya disponible en la app. Luego se hacen los requests, que sus respuestas pasarán por el store y actualizarán la información en el appState, cuando los requests pasen al estado de `DATA_RECEIVED` o `ERROR_RECEIVED`.

Para mostrar cambios debido al lanzamiento de los requests se crea la función `processResponse`.

Los valores del componente se inicializan basándose en el `localStorage` así:
```typescript
const [local, updateLocal] = useImmer(() => {
  const appState = AppState.getState();
  return {
    // Inicialización de valores basándose en appState
    openedAt: null as null | number,
  };
});
useEffect(() => {
  updateLocal((draft) => {
    draft.openedAt = Date.now();
  });
}, []); // eslint-disable-line react-hooks/exhaustive-deps
```
...si bien uno podría pensar que pueden simplificarse a esta forma ya que en algunos componentes esto funciona bien:
```typescript
const [local, updateLocal] = useImmer(() => {
  const appState = AppState.getState();
  return {
    // Inicialización de valores basándose en appState
    openedAt: Date.now(),
  };
});
```
...usar la inicialización en la que se pone `null` en `openedAt` dentro de la callback de `useImmer`, ya que en algunos componentes la otra forma simplificada no funcionaba bien.

En [setActiveReservationOption.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/reservation/setActiveReservationOption.ts) se describe cómo que debo responder desde el backend para que se actualice el Apollo caché. Se actualiza el resultado de las queries presentes en el componente gracias al `subscribe` de Apollo, disparando una action `DATA_RECEIVED` en el store, que las funciones en [storeUtils.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/shared/storeUtils.ts) `mutate` o `getSubscription` se ocupan de aplicar al appState.

Cuando desde el backend no se retornan los valores actualizados al lanzar una mutation, se puede ver de actualizar `appState` usando los valores que se enviaron en la request, como en [saveStore.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/reservation/saveStore.ts).

Si en el request necesito valores globales como `userId` y `lng` en sus argumentos, puedo adquirirlos directamente desde el request sin tener enviárselos desde el componente.

El argumento `fetchPolicy` de `client
.watchQuery` en [storeUtils.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/shared/storeUtils.ts) es de tipo `WatchQueryFetchPolicy`, y sus opciones son: `cache-first` | `network-only` | `cache-only` | `no-cache` | `standby` | `cache-and-network`, [doc](https://www.apollographql.com/docs/react/data/queries#supported-fetch-policies).

Para debuggear puede ser útil introducir un valor corrupto en el appState. Por ejemplo, se puede ir a la página Reservas para que se carguen las existentes y cargar un `reminder` inválido mediante la consola:
```javascript
newState = JSON.parse(localStorage.appState)
newState.reservation.reservations[0].reminder = 'invalid'
localStorage.setItem('appState', JSON.stringify(newState))
```
Como la `appState` se lee de la memoria RAM, para que esto tengo efecto debo modificar `AppState.getState()` para que devuelva el valor del `localStorage`.

## Ejemplos para usar como referencia

Mantener estos ejemplos actualizados incluyendo las mejores prácticas que voy incorporando.

- Ejemplos de requests: 
    - Get simple que pone los datos recibidos en una variable local del componente, `launchGetBalanceRows` en [BalanceList.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/balance/BalanceList.tsx).
    - Get que se puede relaunchear para actualizar datos: `getAvailability` en [Schedule.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/schedule/Schedule.tsx)
    - Formulario [GeneralSettings.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/configure/1main/GeneralSettings.tsx) con input de números, checkbox y selects con unidades. Manejo de errores en tiempo real y backend. Casteo de valores recibidos de request. Validación de argumentos de request [saveGSreservations.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/saveGSreservations.ts) usando [MyButton.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/components/MyButton.tsx) y `validateArgs` importada del use case del backend [UpdateGSreservations](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/updateGSreservations/UpdateGSreservations.ts). El type `<Request>Args` en el archivo del request, es como el `Request` de `<BackenEndUseCase>DTOs.ts` pero sin aquellos argumentos que permanecen igual en todos los requests como `userId` o que se puede adquirir globalmente como `lng`. Se usa lo recibido por `getGSforReservations` para llenar los campos del formulario. Todos los requests necesitados para poblar los datos iniciales, son launcheados por el padre (por ejemplo [Configure.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/configure/Configure.tsx)), los children escuchan aquellos requests en los que están interesados. Los requests de mutaciones son manejados completamente por los children.
    - Formulario [2Account.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/profile/2Account.tsx) como `GeneralSettings.tsx`, pero incluyendo selects simples (sin unidades) y select con buscador de opciones.
    - Select que su borde se pone rojo si hay errores: ver columna `reminder` y campo `local.newStatus` en [ReservationTable.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/reservations/table/ReservationTable.tsx)
    - Para el completado inicial de los formularios con los datos del localStorage, se usa la callback de `useImmer`.
- Otro ejemplo de un formulario complejo (aunque con implementaciones un poco más viejas que `GeneralSettings.tsx`) con campos `number`, `string`, GraphQL `Float`, nullables y `boolean`: [CreateEditCommon_.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/configure/common/CreateEditCommon_.ts). Cuando hay errores en el formulario, además de deshabilitarse el botón, agrego texto indicando la presencia de errores ya que el formulario es largo. Ver en conjunto con [CreateReservationOption.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/configure/2creating/CreateReservationOption.tsx) (backend use case [CreateReservationOption.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/createReservationOption/CreateReservationOption.ts)) o [EditReservationOption.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/configure/3editing/EditReservationOption.tsx) (backend use case [UpdateReservationOption.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/updateReservationOption/UpdateReservationOption.ts))
- Ejemplo de un request disparado por un checkbox: ver `setActiveReservationOption` en [ReservationOptions.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/configure/1main/ReservationOptions.tsx)
- Espaciamiento usando `bundle` y `title`: [2Account.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/profile/2Account.tsx)
- Modal: [CancelReservationModal.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/components/CancelReservationModal.tsx) usado en [ReservationsNav.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/reservations/ReservationsNav.tsx).

Ejemplos relacionados con las traducciones:
- Uso de `TransForComponent` en [2Account.trans.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/profile/2Account.trans.ts) y poner en el componente de manera similar a [2Account.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/profile/2Account.tsx):
    ```tsx
    import { useTranslation } from 'react-i18next';
    import { TransForAccount } from './2Account.trans.ts';

    const trans = new TransForAccount();
    const commonI18n = commonTrans.getI18nForComponent();

    export function Component() {
        const { t } = useTranslation(trans.ns, {
            i18n: commonI18n,
            lng: useTranslation().i18n.language,
        });
    
        return (
            <div>
                {t('save')}
            </div>
        );
    }
    ```
- Compartir traducciones mediante [Common.trans.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/trans/Common.trans.ts). Usar `commonT` en el componente para diferenciarlo del `t` local del componente:
    ```tsx
    import { useTranslation } from 'react-i18next';
    import { CommonTrans } from './Common.trans.ts';

    const commonTrans = new CommonTrans();
    const commonI18n = commonTrans.getI18nForComponent();

    export function Component() {
        const { t: commonT } = useTranslation(commonTrans.ns, {
            i18n: commonI18n,
            lng: useTranslation().i18n.language,
        });
    
        return (
            <div>
                {commonT('save')}
            </div>
        );
    }
    ```

Fragmentos de código
- Argumentos de una función mutuamente exclusivos: argumentos `showErrors`, `errorMsg` y `skipErrors` de `processResponse`, verificados por `getErrorHandler` en [storeUtils.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/shared/storeUtils.ts)

### Ejemplo de tabla

Tabla de manually created customers<br /><br />
[Customers.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/customers/Customers.tsx)
```tsx
function Customers() {
  return (
      <MCustomersNav />
  );
}
```
[table/core.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/customers/mcustomer/table/core.ts)
```typescript
// Configuration for tables and shared code
import { user } from '@s4nt14go/book-backend';

type ManuallyCreatedCustomerDtoWithTimestamps = user.ManuallyCreatedCustomerDtoWithTimestamps;

type EditMCustomer = (args: {
  customer: ManuallyCreatedCustomerDtoWithTimestamps;
  pageIndex: number;
}) => void;
type RemoveMCustomer = (entity: ManuallyCreatedCustomerDtoWithTimestamps) => void;

// more code...
```
[MCustomersNav.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/customers/mcustomer/MCustomersNav.tsx)
```tsx
// Handles navigation between the list, creation, and editing views, as well as the modal for removal and its rmMCustomer request processing
import { EditMCustomer, RemoveMCustomer } from './table/core.ts';
function MCustomersNav() {
  const [local, updateLocal] = useImmer(() => {
    return {
      show: 'list' as 'list' | 'creating' | 'editing',
      customerEditing: null as ManuallyCreatedCustomerDtoWithTimestamps | null,
      initialPageIndex: 0,
      confirmRemoveOpen: false,
      customerRemoving: null as ManuallyCreatedCustomerDtoWithTimestamps | null,
      isRemoving: false,
    }
  });

  function go2list() {
    updateLocal((draft) => {
      draft.show = 'list';
    });
  }

  function go2forSuccess() {
    updateLocal((draft) => {
      draft.show = 'list';
    });
  }

  const edit: EditMCustomer = (args) => {
    const { customer, pageIndex } = args;
    updateLocal((draft) => {
      draft.show = 'editing';
      draft.customerEditing = customer;
      draft.initialPageIndex = pageIndex;
    });
  }

  const remove: RemoveMCustomer = (customer) => {
    updateLocal((draft) => {
      draft.confirmRemoveOpen = true;
      draft.customerRemoving = customer;
    });
  }
  return (
    <>
      {local.show === 'list' && (
        <>
          <MCustomersList
            edit={edit}
            remove={remove}
            initialPageIndex={local.initialPageIndex}
          />
          <button
            onClick={() => {
              updateLocal((draft) => {
                draft.show = 'creating';
                draft.initialPageIndex = 0;
              });
            }}
          >
            Crear nuevo cliente
          </button>
        </>
      )}

      {local.show === 'creating' && (
        <>
          <GoBack text="Volver" clickOnBack={go2list} />
          <MCustomerCreate 
            clickOnCancel={go2list} 
            go2forSuccess={go2list} 
          />
          
        </>
      )}

      {local.show === 'editing' && local.customerEditing && (
        <>
          <GoBack text="Volver" clickOnBack={go2list} />
          <MCustomerEdit
            clickOnCancel={go2list}
            go2forSuccess={go2forSuccess}
            customer={local.customerEditing}
          />
        </>
      )}

      {local.customerRemoving && (
        <Dialog
          open={local.confirmRemoveOpen}
          onClose={() => {
            updateLocal((draft) => {
              draft.confirmRemoveOpen = false;
            });
          }}
        >
          
          {/*The actual dialog panel*/}
          <DialogPanel>
            <DialogTitle>
              ...
            </DialogTitle>
            <Description>
              ...
            </Description>
          
            <MyButton checkOnClick={confirmRemove()}
                  inProgress={local.isRemoving}
                  request={launchRmMCustomer} color='red' text='BORRAR' />

            <button
              onClick={() => {
                updateLocal((draft) => {
                  draft.confirmRemoveOpen = false;
                });
              }}>
                CANCELAR
            </button>
          </DialogPanel>
          
        </Dialog>
      )}
    </>
  );
}
```
[MCustomerCreate.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/customers/mcustomer/MCustomerCreate.tsx)
```tsx
import { CommonLocal } from './MCustomerCommon.tsx';

function MCustomerCreate(props: {
  clickOnCancel: () => void;
  go2forSuccess: () => void;
}) {

  return (
    <>
      <MCustomerCommon
        local={local}
        updateLocal={updateLocal}
      />

      <MyButton checkOnClick={falseOrArgs} inProgress={inProgress}
                request={launchNewMCustomer} text='CREAR' />

      <CancelButton inProgress={inProgress} clickOnCancel={clickOnCancel} />
    </>
  );
}
```
[MCustomerEdit.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/customers/mcustomer/MCustomerEdit.tsx)
```tsx
import { CommonLocal } from './MCustomerCommon.tsx';

function MCustomerEdit(props: {
  clickOnCancel: () => void;
  go2forSuccess: () => void;
  customer: MCustomer;
}) {

  return (
    <>
      <MCustomerCommon
        local={local}
        updateLocal={updateLocal}
      />

      <MyButton checkOnClick={!falseOrArgs? false : Object.assign({}, falseOrArgs, { id: customer.id })} inProgress={inProgress}
                request={launchEditMCustomer} text='EDITAR' />

      <CancelButton inProgress={inProgress} clickOnCancel={clickOnCancel} />
    </>
  );
}
```
[MCustomersList.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/customers/mcustomer/MCustomersList.tsx)
```tsx
// Retrieves and updates the MCustomers list, handling getMCustomers and rmMCustomer requests.
// Since receiving a successful newMCustomer request in MCustomerCreate.tsx automatically returns to MCustomersList.tsx, where it launches getMCustomers and updates the list, there is no need to handle the newMCustomer request separately in MCustomersList.tsx.
// Since receiving a successful editMCustomer request returns the updated version of MCustomer with __typename ManuallyCreatedCustomerWithTimestamps, which is the same type received by getMCustomers, getMCustomers auto-updates. Additionally, similarly to MCustomerCreate.tsx, after receiving a successful editMCustomer request, MCustomerEdit.tsx returns to MCustomersList.tsx, where it luanches a new getMCustomers, updating the list once more. Therefore, there is no need to handle editMCustomer request in MCustomersList.tsx.
import { EditMCustomer, RemoveMCustomer } from './table/core.ts';
function MCustomersList(props: {
  edit: EditMCustomer;
  remove: RemoveMCustomer;
  initialPageIndex: number;
}) {

  return (
      {
        local.manuallyCreatedCustomers ?
          <MCustomersTable
            data={local.manuallyCreatedCustomers}
            edit={edit}
            remove={remove}
            initialPageIndex={initialPageIndex}
          />
          :
          <p className="mt-14 text-2xl text-center mb-14">
            Actualmente no hay clientes creados
          </p>
      }
  );
}
```
[table/core.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/customers/mcustomer/table/core.ts)
```typescript
// Configuration for tables and shared code
import { user } from '@s4nt14go/book-backend';

type ManuallyCreatedCustomerDtoWithTimestamps = user.ManuallyCreatedCustomerDtoWithTimestamps;

type EditMCustomer = (args: {
  customer: ManuallyCreatedCustomerDtoWithTimestamps;
  pageIndex: number;
}) => void;
type RemoveMCustomer = (entity: ManuallyCreatedCustomerDtoWithTimestamps) => void;

const columnsDisplayed = [
  { firstName: 'Nombre' },
  { lastName: 'Apellido' },
  { notes: 'Notas' },
  { created_at: 'Creado' },
  { updated_at: 'Modificado' },
  { actions: 'Acciones' },
  { id: 'ID del cliente' },
]  as const;

type ColumnsDisplayedKeys = 
  'firstName' |
  'lastName' |
  'notes' |
  'created_at' |
  'updated_at' |
  'actions' |
  'id';

type MCustomer = ManuallyCreatedCustomerDtoWithTimestamps & {
  // Add the columns that aren't in the original type
  actions: null; // there is no need to set a text, since this column is not searchable (enableGlobalFilter: false).
};
```
[table/MCustomersTable.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/customers/mcustomer/table/MCustomersTable.tsx)
```tsx
// Table cell display for desktop version. This component toghether with TableDesktop.tsx displays the desktop version.
// It also configures and sets data for react-table, its data is used by both the desktop and mobile versions.
import { columnsDisplayed, MCustomer } from './core.ts';
function MCustomersTable(props: {
  data: ManuallyCreatedCustomerDtoWithTimestamps[];
  edit: EditMCustomer;
  remove: RemoveMCustomer;
  initialPageIndex: number;
}) {
  const {data: _data, edit, remove, initialPageIndex} = props;
  const appState = AppState.getState();

  const [columnVisibility, setColumnVisibility] = useState(
    appState.user.manuallyCreatedCustomersVisible,
  );

  const [sorting, setSorting] = useState<SortingState>(appState.user.manuallyCreatedCustomersSorting);
  useEffect(() => {
    AppState.setMCustomersSort(sorting);
  }, [sorting]);

  const [tableData, setTableData] = useState<MCustomer[]>([]);
  useEffect(() => {
    setTableData(_data.map((customer) => ({
      ...customer,
      // Set a value for the added columns of MCustomer in core.ts. If this column is searchable 
      // through filter (default enableGlobalFilter: true), the content text should be set as with 
      // created_at and updated_at. See "description" field in
      // src/pages/balance/table/BalanceTable.tsx for another example.
      actions: null,
      created_at: format2BrowserDate({ value: customer.created_at } ),
      updated_at: format2BrowserDate({ value: customer.updated_at } ),
    })));
  }, [_data]);

  const columns = [];

  for (const key of columnsDisplayed.map((a) => Object.keys(a)[0])) {
    switch (key) {
      case 'firstName':
        columns.push(
          columnHelper.accessor('firstName', {
            header: columnsDisplayed.find((a) => a.firstName)?.firstName,
          }),
        );
        break;
        ...
      case 'created_at':
        columns.push(
          columnHelper.accessor('created_at', {
            header: columnsDisplayed.find((a) => a.created_at)?.created_at,
          }),
        );
        break;
        ...
      case 'actions':
        columns.push(
          columnHelper.accessor('actions', {
            header: columnsDisplayed.find((a) => a.actions)?.actions,
            cell: (props) => {
              const {row, table} = props;
              return <Actions
                edit={() =>
                  edit({
                    customer: row.original,
                    pageIndex: table.getState().pagination.pageIndex,
                  })
                }
                remove={() => remove(row.original)}
              />;
            },
            enableSorting: false,
            enableGlobalFilter: false,
          }),
        );
        break;
    }
  }

  const [globalFilter, setGlobalFilter] = useState('');

  const table = useReactTable({
    columns,
    data: tableData,
    state: {
      columnVisibility,
      globalFilter,
      sorting,
    },
    onColumnVisibilityChange: setColumnVisibility,
    onSortingChange: setSorting,
    onGlobalFilterChange: setGlobalFilter,
    initialState: {
      pagination: {
        pageSize: appState.user.manuallyCreatedCustomersPageSize,
        pageIndex: initialPageIndex,
      },
    },
  });

  return  <Table<MCustomer, ColumnsDisplayedKeys>  
              table={table} columnVisibility={columnVisibility}
              globalFilter={globalFilter} setGlobalFilter={setGlobalFilter}
              savePageSize={AppState.setMCustomersPageSize}
              saveVisible={AppState.setMCustomersVisibility}
              editMCustomer={edit} removeMCustomer={remove}
          />;
}
```
[Table.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/components/Table.tsx)
```tsx
// Shared code for all tables in the app, showing desktop or mobile version depending on tailwind "table" breakpoint
function Table<Data>(props: {
  // Common props for all tables
  // ...
  // For MCustomers
  editMCustomer?: EditMCustomer;
  removeMCustomer?: RemoveMCustomer;
  // For Reservations
  showModalCustomer?: ShowModalCustomer;
  showModalCancelReservation?: ShowModalCancelReservation;
  bgCondition?: (original: Data) => boolean;
  bgClass?: string;
}) {
  return <>
    {/* Visibility checkboxes */}
    <TableVisible table={table} saveVisible={saveVisible} />

    {
      Object.values(columnVisibility).some(v => v === true) ?
        <>
          {/* table:hidden */}
          <TableMobile rows={rows} getTableState={table.getState}
                       // For MCustomers
                       editMCustomer={editMCustomer} removeMCustomer={removeMCustomer}
                       // For Reservations
                       showModalCustomer={showModalCustomer} showModalCancelReservation={showModalCancelReservation}
          />
          
          <TableDesktop table={table} bgCondition={bgCondition} bgClass={bgClass} /> {/* hidden table:block */}

          <TableFooters table={table} globalFilter={globalFilter} setGlobalFilter={setGlobalFilter} savePageSize={savePageSize} />
        </>

        : <SelectAcolumn />
    }
    
  </>;
}
```
[TableDesktop.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/components/TableMain.tsx)
```tsx
// Shared code for desktop tables in the app. This component toghether with MCustomersTable.tsx displays the desktop version.
function TableDesktop<Data>(props: {
  table: _Table<Data>;
  bgCondition?: (original: Data) => boolean;
  bgClass?: string;
}) {
  
  return <div className="hidden table:block">
    <table>
      <TableHeader table={table} />
      <tbody>
      {table.getRowModel().rows.map((row) => (
        <tr key={row.id} className={`${ bgCondition && bgCondition(row.original)? bgClass : ''}`}>
          ...
        </tr>
      ))}
      </tbody>
    </table>
  </div>;
}
```
[TableMobile.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/components/TableMobile.tsx)
```tsx
// Shared code for mobile tables in the app, it determines which entity's mobile table to display based on the data cell. This component toghether with MCustomersMobileRow.tsx displays the mobile version.
import MCustomersMobileRow from '../pages/customers/mcustomer/table/MCustomersMobileRow.tsx';
import {
  EditMCustomer,
  isMCustomer,
  RemoveMCustomer
} from '../pages/customers/mcustomer/table/core.ts';
import ReservationsMobileRow from '../pages/reservations/table/ReservationsMobileRow.tsx';
import {
  bgCondition,
  bgClass,
  isReservation,
  ShowModalCancelReservation,
  ShowModalCustomer,
} from '../pages/reservations/table/core.ts';

function TableMobile<Data>(props: {
  // Available in all tables even though not used in all
  rows: Row<Data>[];
  getTableState: () => TableState;
  // For MCustomers
  editMCustomer?: EditMCustomer;
  removeMCustomer?: RemoveMCustomer;
  // For Reservations
  showModalCustomer?: ShowModalCustomer;
  showModalCancelReservation?: ShowModalCancelReservation;
}) {
  
  const appState = AppState.getState(); 
  const { timezone: userTz } = appState.user;

  return <div className='table:hidden'>

    <hr className="border-2"/>
    {
      rows.map(row => {
        const original = row.original;

        if (isMCustomer(original)) {
          if (!editMCustomer || !removeMCustomer) throw Error('Required prop not defined');
          return <MCustomersMobileRow ...
          />
        }

        if (isReservation(original)) {
          if (!showModalCustomer || !showModalCancelReservation || !userTz)
            throw Error('Required prop not defined');
          return <ReservationsMobileRow ...
        }
      })
    }

  </div>;
}
```

> Notar que hay que agregar todas las tablas mobile en el componente común `TableMobile.tsx`.

[table/MCustomersMobileRow.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/customers/mcustomer/table/MCustomersMobileRow.tsx)
```tsx
// Row display for mobile version
import {
  columnsDisplayed,
  ColumnsDisplayedKeys,
  MCustomer,
} from './core.ts';
import Actions from './Actions.tsx';

function MCustomersMobileRow(props: {
  getTableState: () => TableState;
  edit: EditMCustomer;
  remove: RemoveMCustomer;
  customer: MCustomer;
  columns: Cell<MCustomer, unknown>[];
  rowId: string;
}) {

  return columns.map((c, i) => {
    const key = c.column.id as ColumnsDisplayedKeys;
    const value = customer[key];
    const label = columnsDisplayed.find(a => a[key])[key];
    switch (key) {
      case 'actions':
        return <div>
          <p>
            {label}:
          </p>
          <Actions
            editMCustomer={() =>
              editMCustomer({
                customer,
                pageIndex: getTableState().pagination.pageIndex,
              })
           }
           remove={() => remove(customer)}
          />
        </div>;
      default:
        return <div>
          {label}: {value ?? '---'}
        </div>
    }
  });
}
```

> Como otro ejemplo de tabla de mayor complejidad se puede ver [ReservationsNav.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/reservations/ReservationsNav.tsx)]

#### Traducciones

Para la traducción de tablas no se usa `TransForComponent`, ver como ejemplo [trans.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/reservations/table/trans.ts). Mientras que se leen de esta forma:

[core.ts](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/reservations/table/core.ts)
```ts
import { getTrans } from './trans.ts';

const columnsDisplayed = [
  { customerType: getTrans('customerType') },
  ...
] as const;

function getCustomerType(value: string) {
  const possibleValues = Object.keys(CustomerType);
  if (possibleValues.includes(value)) return value as CustomerTypeT;
  throw Error(`Unhandled customerType ${value}`);
}
function getCustomerTypeText(value: string) {
  const ct = getCustomerType(value);
  return ct === CustomerType.MANUALLY_CREATED
    ? getTrans('manuallyCreated')()
    : getTrans('registered')();
}
```

[ReservationsTable.tsx](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/reservations/table/ReservationsTable.tsx)
```tsx
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';

// Workaround 1/2 to make getCurrentLng() detect a language change and re-trigger getReminderText()
const commonI18n = i18next.createInstance({ lng: fallbackLng }, () => Object());

function ReservationsTable() {
  // Workaround 2/2 to make getCurrentLng() detect a language change and re-trigger getReminderText()
  useTranslation(undefined, {
    i18n: commonI18n,
    lng: useTranslation().i18n.language,
  });

  const columns = [];

  for (const key of columnsDisplayed.map((a) => Object.keys(a)[0])) {
    switch (key) {
      case 'customerType':
        columns.push(
          columnHelper.accessor('customerType', {
            header: columnsDisplayed.find(
              (a) =>
                a.customerType,
            )?.customerType(),  // --> notar que es una función
          }),
        );
        break;
        ...
    }
  }

  useEffect(() => {
    setTableData(
      _data.map((r) => {
        return {
          ...r,
          customerType: getCustomerTypeText(r.customerType),
        };
      }),
    );
  }, [_data, userTz, getCurrentLng()]);
}
````

## Netlify

Para hacer deploy en Netlify, tuve que dejar de ignorar y agregar a git las librerías `s4nt14go-book-backend-0.0.1.tgz` y `s4nt14go-book-backend-dev-0.0.1.tgz`.

Configuré para que deploye todas las branches mediante: Site Configuration > Branches and deploy contexts. Configuré como branch de producción `prod`, que aún no existe ya que por ahora sigo trabajando en `master` que es mi branch de development.

Configuré las env vars leídas por [configStage.ts](https://github.com/s4nt14go/turnero-front/blob/master/configStage.ts) en Site Configuration > Environment variables:
```dotenv
NODE_VERSION=20
VITE_dev_DOMAIN=...
VITE_dev_GOOGLE_CALENDAR_SERVICE=...
VITE_dev_GRAPHQL_KEY=...
VITE_dev_GRAPHQL_URL=...
```

Por cada push que realizo a `master` se sube a https://master--turnero-s4nt14go.netlify.app

Si Netlify da el error: 
```text
npm error sha512-... integrity checksum failed
```
por `@s4nt14go/book-backend` o `@s4nt14go/book-backend-dev`, se resuelve incrementando sus versiones en el backend (campos `version` y script `cp-to-front`), exportándolas, actualizando las versión que se instala en el frontend (script `install-backend`) e instalando esas nuevas versiones en el frontend.

Por lo que leí, el error se debería a que a mis módulos locales `@s4nt14go/book-backend` y `@s4nt14go/book-backend-dev`, siempre los instalo con el mismo nombre y su suma de comprobación se almacena en package-lock.json. Cuando cambio el contenido del módulo, pero no el nombre, npm detecta que intento instalar el mismo módulo, pero la suma de comprobación no coincide.

## Depurar en el celu

> Probé el debugging con WiFi. Usa Android Studio y aparentemente está orientado a aplicaciones nativas, no pude cambiar el css de la app como lo hago con Chrome. Mejor usar el debugging con USB usando Chrome, como se explica a continuación.

En mi celu ir a Ajustes > Sistema > Acerca del teléfono > Número de compilación y tocar 7 veces para activar las opciones de desarrollador.

Luego ir a Ajustes > Sistema > Opciones de desarrollador y habilitar Depuración USB.

Con el celu enchufado al USB, en Chrome ir a chrome://inspect/#devices dar permiso en el celu y puedo hacer in inspect del Chrome siendo mostrado en el celu.

## Testing

Para los componentes utilizo React Testing Library ([doc](https://testing-library.com/docs/react-testing-library/intro/)).

Para la instalación además de hacer lo que dice la doc:
```shell
npm i -D @testing-library/react @testing-library/dom @types/react @types/react-dom
```
...tuve que instalar `json`:
```shell
npm i -D json
```
y agregar en `vite.config.ts`:
```json5
{
  test: {
    environment: 'jsdom', // Si no, daba error: document is not defined
    globals: true,  // Si no, al poner más de un render daba error: Found multiple elements
  }
}
```

[Testing Playground](https://testing-playground.com/): Página con ejemplos de inputs, checkboxes y botones.

Extensión de Chrome [Testing Playground](https://chrome.google.com/webstore/detail/testing-playground/hejbmebodbijjdhflfknehhcgaklhano), que agrega una solapa en las dev tools y muestra los selectores de los elementos en la página.

Para grabar un script se utiliza la tab "Grabadora" de las dev tools: 3 puntitos verticales de Dev Tools > Más herramientas > Grabadora ([fuente](https://developer.chrome.com/docs/devtools/recorder?hl=es-419)). Instalar la extensión que permite exportar el script para [React Testing Library](https://chromewebstore.google.com/detail/testing-library-recorder/pnobfbfcnoeealajjgnpeodbkkhgiici) y [Cypress](https://chromewebstore.google.com/detail/cypress-chrome-recorder/fellcphjglholofndfmmjmheedhomgin).

[screen.logTestingPlaygroundURL()](https://testing-library.com/docs/dom-testing-library/api-debugging/#screenlogtestingplaygroundurl) genera un link con el código de la pantalla actual en [Testing Playground](https://testing-playground.com/). Para logear el componente en la consola [screen.debug()](https://testing-library.com/docs/dom-testing-library/api-debugging/#screendebug).

Instalación de Cypress:
```shell
npm i -D cypress
```
Agregar en `package.json` (es necesario estar ejecutando el servidor de desarrollo):
```json5
"scripts": {
"cy:open": "cypress open",  --> abre UI, si los tests empienzan a fallar sin sentido reiniciar kill a `cypress` y ejecutar devuelta. La primera vez genera una carpeta `cypress` con ejemplos de tests.
"test-e2e": "cypress run"   --> ejecuta todos los test sin abrir UI
}
```

### Recursos útiles

[Asserting elements are not present](https://stackoverflow.com/questions/52783144/how-do-you-test-for-the-non-existence-of-an-element-using-jest-and-react-testing#answer-52783201). [Tabla comparando get/find/query](https://stackoverflow.com/questions/52783144/how-do-you-test-for-the-non-existence-of-an-element-using-jest-and-react-testing#answer-74387094). Otra opción es buscar la class `hidden`:
```javascript
expect(screen.getByTestId('disconnectAvailability').parentElement!.classList.contains('hidden')).toBe(true);
```

## Librerías que pueden ser útiles

Pongo las librerías que me pueden ser útiles para el proyecto turnero pero en el artículo de Robin Wieruch hay más.

Comparar cuánto se usan mediante https://www.npmjs.com y https://npmtrends.com

Artículo [React Libraries 2024](https://www.robinwieruch.de/react-libraries) de Robin Wieruch
- Aplicar classNames condicionalmente [clsx](https://github.com/lukeed/clsx)
- [Recharts](https://recharts.org)
- [React Hook Form](https://react-hook-form.com) with [zod](https://zod.dev) integration for validation
- [react-i18next](https://react.i18next.com) ([tuto](https://www.robinwieruch.de/react-internationalization)) Commit de [ejemplo](https://github.com/s4nt14go/turnero-front/commit/a57566163635f37771ad6507c583be1f4e8b491a) al agregarlo ([enable debug in DEV](https://github.com/s4nt14go/turnero-front/commit/5edd72c2f3d0253998b8feaf2d1a753192fb83b9)). Por ejemplos de componentes para usar como referencia ver dicha sección.
- [Payments in react](https://www.robinwieruch.de/react-libraries/#payments-in-react)

Artículo [React Trends](https://www.robinwieruch.de/react-trends):
- [Astro](https://astro.build) for content-driven websites

Artículo [React Tech Stack 2025](https://www.robinwieruch.de/react-tech-stack)

Otras:
- Toasts: [sonner](https://sonner.emilkowal.ski), [react-hot-toast](https://react-hot-toast.com), [react-toastify](https://fkhadra.github.io/react-toastify/) [trends](https://npmtrends.com/react-hot-toast-vs-react-toastify-vs-sonner) Sacados de [State of React survey](https://stateofreact.com/en-US)
- [react email](https://react.email)

### Tutoriales

Son del canal de YouTube [Codevolution](https://www.youtube.com/@Codevolution), que es de donde saqué [Node.js Tutorial](https://www.youtube.com/watch?v=LAUi8pPlcUM&list=PLC3y8-rFHvwh8shCMHFA5kWxD9PaPwxaY&t=51s), puesto en el repo [challenges](https://github.com/s4nt14go/challenges/blob/main/README.md), que me pareció muy bueno.

- [React Native Tutorial](https://www.youtube.com/watch?v=hzzCveeczSQ&list=PLC3y8-rFHvwhiQJD1di4eRVN30WWCXkg1) 5h 43m 2023
- [React Hook Form](https://www.youtube.com/watch?v=KejZXxFCe2k&list=PLC3y8-rFHvwjmgBr1327BA5bVXoQH-w5s) 2h 4m 2023
- [React Testing Tutorial](https://www.youtube.com/watch?v=T2sv8jXoP4s&list=PLC3y8-rFHvwirqe1KHFCHJ0RqNuN61SJd) 4h 39m 2022
- [React Router Tutorial](https://www.youtube.com/watch?v=UWYOC8g5N_0&list=PLC3y8-rFHvwjkxt8TOteFdT_YmzwpBlrG) 1h 37 2022
- [React Table Tutorial](https://www.youtube.com/watch?v=YwP4NAZGskg&list=PLC3y8-rFHvwgWTSrDiwmUsl4ZvipOw9Cz) 1h 47m 2020
- [Chrome Extension Tutorial](https://www.youtube.com/watch?v=8q1_NkDbfzE&list=PLC3y8-rFHvwg2-q6Kvw3Tl_4xhxtIaNlY) 2h 3m 2016

## Google Analytics

Consulté a `gemini-2.5-pro-preview-05-06` y `claude-3-7-sonnet-20250219` qué recomendaban para analytics y ambos me contestaron Google Analytics 4 (GA4). Sin embargo, no me convenció que los eventos que se pueden enviar tienen una estructura muy rígida:
```ts
ReactGA.event({
  action: string;
  category: string;
  label?: string;
  value?: number;
  nonInteraction?: boolean;
  transport?: ('beacon' | 'xhr' | 'image');
});
```
Logré enviar un evento con propiedades custom mediante:
```ts
gtag('event', 'santi', {
  a: 'a',
  b: 2,
  c: true,
  d: new Date().toISOString(),
  e: ['item1', 'item2'],
});
```
Sin embargo, la visualización de los datos de los eventos en la consola https://analytics.google.com/analytics/ es muy mala (se ven en `Reports` -> `Realtime overview`: card `Event count
 by Event name`). A su vez, a los eventos custom, enviados con `gtag`, no se los puede filtrar al ir a `Reports` -> `View user engagement & retention` -> `Events`. Lo que sí es cierto es que puede ayudar a conocer a mi audiencia, por lo que decido dejar con una configuración básica.

### Configuración de GA4

Crear cuenta para el sitio web ("Turnero", en la captura de abajo) y propiedad ("Prueba1")
<p align="center">
    <img src="doc/ga/createAccount.png" />
</p>

Crear data stream para el sitio web
<p align="center">
    <img src="doc/ga/dataStream.png" />
</p>

En "View tag instructions" sale lo que hay que copiar en el `index.html`, abajo de `<head>`
<p align="center">
    <img src="doc/ga/tagInstructions.png" />
</p>

En "Property details" sale el `Measurement ID`
<p align="center">
    <img src="doc/ga/propertyDetails.png" />
</p>

Inicializar el property en `main.tsx`:
```ts
import ReactGA from 'react-ga4';
ReactGA.initialize('G-*********');
```

Configurar `userId` y evento `pageview` en `App.tsx`:
```ts
ReactGA.set({ userId });

// ...

function Layout() {
  const location = useLocation();
  const path = location.pathname;

  useEffect(() => {
    ReactGA.send({ hitType: "pageview", page: path + location.search });
  }, [location]);
}
```

Enviar eventos en los componentes:
```jsx
<button
  onClick={() => {
    ReactGA.event({
      category: 'User Interaction',
      action: '6 Clicked Call To Action Button',
      value: 1,
      label: 'label1',
    });

    // Ejemplo de evento custom:
    gtag('event', 'santi', {
      a: 'a',
      b: 2,
      c: true,
      d: new Date().toISOString(),
      e: ['item1', 'item2'],
    });
  }}
>
  Enviar evento
</button>
```



## Demo usando stage `dev`

Para hacer las screenshots y videos de demostración salgan los valores que van a ser usados en producción, hago estos cambios temporales para usar stage `dev`:
- Pongo en `.env` `VITE_dev_GOOGLE_CALENDAR_SERVICE=<EMAIL>`
- En el Parameter Store, parameter `/turnero/dev/calendar` pongo los campos `client_email` y `private_key` con los valores de `driven-strength-462013-a0-928f4bbfec5a.json` (en lugar de los de `prueba1--s4nt14godev-3528f2be01e5.json`) https://us-east-1.console.aws.amazon.com/systems-manager/parameters/%252Fturnero%252Fdev%252Fcalendar/edit?region=us-east-1&tab=Table
- Volver a deployar backend así toma los nuevos valores de `client_email` y `private_key` que son configurados por `MyStack.ts`. 

## CSS Grid

UPDATE: En realidad terminé usando Tailwind para el layout pero por el momento dejo esta sección acerca de aprender cómo funciona CSS Grid en el caso de que sea útil saberlo pero como mencioné, no lo usé.

CSS Grid me parece algo muy práctico, esta playlist [CSS Grid](https://www.youtube.com/playlist?list=PLu8EoSxDXHP5CIFvt9-ze3IngcdAc2xKG) es muy buena.

Si me acuerdo los fundamentals puedo ver directamente el video [Responisve Website Exercise — 24 of 25](https://www.youtube.com/watch?v=6dzzVGXVflI&list=PLu8EoSxDXHP5CIFvt9-ze3IngcdAc2xKG&index=24) para ejemplo de un layout responsivo. Si necesito un refresh de los fundamentals antes de ver ese video ver  [CSS Grid in 45 Minutes!](https://www.youtube.com/watch?v=DCZdCKjnBCs&list=PLu8EoSxDXHP5CIFvt9-ze3IngcdAc2xKG&index=26). Creo que con esos dos videos ya debería ser suficiente, y en todo caso elegir algunos otros puntuales de la playlist.

Para referencia:
- [CSS Tricks: A Complete Guide to CSS Grid](https://css-tricks.com/snippets/css/complete-guide-grid/#grid-properties)
- [Poster](https://css-tricks.com/wp-content/uploads/2022/02/css-grid-poster.png)